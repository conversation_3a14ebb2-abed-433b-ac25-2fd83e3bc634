class KafkaCampaignAnalyzer
  attr_reader :start_date, :retailer_ids
  
  def initialize(start_date = '2025-07-31')
    @start_date = start_date
    @retailer_ids = load_retailer_ids
  end
  
  def load_retailer_ids
    Retailer.where(kafka_enabled: true)
            .joins(:campaigns)
            .where('campaigns.created_at >= ?', start_date)
            .distinct
            .pluck(:id)
            .sort
  end
  
  def print_header
    puts "🔍 ANÁLISIS DETALLADO POR RETAILER DESDE 01/08/2025"
    puts "=" * 80
    puts "📊 Analizando #{retailer_ids.count} retailers con kafka_enabled=true y campañas desde #{Date.parse(start_date).strftime('%d/%m/%Y')}"
    puts "🎯 Retailers encontrados: #{retailer_ids.join(', ')}"
  end
  
  def analyze_retailer(retailer_id, index)
    puts "\n" + "="*60
    puts "#{index + 1}/#{retailer_ids.count} - RETAILER ID: #{retailer_id}"
    puts "="*60
    
    retailer = Retailer.find_by(id: retailer_id)
    
    if retailer.nil?
      puts "❌ Retailer no encontrado"
      return
    end
    
    puts "🏪 #{retailer.name}"
    puts "🔧 kafka_enabled: #{retailer.kafka_enabled}"
    
    unless retailer.kafka_enabled
      puts "⚠️  PROBLEMA: Este retailer NO tiene kafka_enabled=true"
      return
    end
    
    campaigns = retailer.campaigns.where('created_at >= ?', start_date)
    puts "📊 Total campañas desde 01/08: #{campaigns.count}"
    
    if campaigns.empty?
      puts "ℹ️  Sin campañas desde el 01/08"
      return
    end
    
    analyze_campaigns(campaigns)
  end
  
  def analyze_campaigns(campaigns)
    kafka_count = campaigns.where(sender_strategy: 'kafka').count
    sync_count = campaigns.where(sender_strategy: 'synchronous').count
    nil_count = campaigns.where(sender_strategy: nil).count
    kafka_adoption = campaigns.count > 0 ? (kafka_count.to_f / campaigns.count * 100).round(1) : 0
    
    puts "🎯 Adopción Kafka: #{kafka_adoption}% (#{kafka_count}/#{campaigns.count})"
    puts "   ✅ Kafka: #{kafka_count} | ⚠️ Sync: #{sync_count} | ❓ Nil: #{nil_count}"
    
    # Estados
    status_counts = campaigns.group(:status).count
    puts "📊 Estados: #{status_counts.map { |k,v| "#{k}:#{v}" }.join(', ')}"
    
    # Últimas 5 campañas
    puts "📋 Últimas 5 campañas desde 01/08:"
    campaigns.order(:created_at).last(5).each do |c|
      strategy_icon = case c.sender_strategy
                     when 'kafka' then '✅'
                     when 'synchronous' then '⚠️'
                     else '❓'
                     end
      status_icon = c.status == 'sent' ? '✅' : c.status == 'processing' ? '🔄' : '❌'
      date_str = c.created_at.strftime('%d/%m %H:%M')
      puts "   #{strategy_icon}#{status_icon} #{c.id} | #{c.sender_strategy || 'nil'} | #{c.status} | #{date_str} | #{c.name[0..25]}..."
    end
    
    analyze_problems(campaigns, kafka_adoption)
  end
  
  def analyze_problems(campaigns, kafka_adoption)
    processing_campaigns = campaigns.where(status: 'processing')
    if processing_campaigns.any?
      puts "🔄 #{processing_campaigns.count} campañas en PROCESSING:"
      processing_campaigns.each do |c|
        puts "   🔄 #{c.id} | #{c.web_id} | #{c.name[0..30]}... | #{c.created_at.strftime('%d/%m %H:%M')}"
      end
    end
    
    sync_count = campaigns.where(sender_strategy: 'synchronous').count
    nil_count = campaigns.where(sender_strategy: nil).count
    
    if sync_count > 0
      puts "🔍 #{sync_count} campañas sincrónicas - investigar por qué no usaron Kafka"
    end
    
    if nil_count > 0
      puts "❓ #{nil_count} campañas sin sender_strategy definido"
    end
    
    problematic = campaigns.where.not(status: 'sent')
    if problematic.any?
      puts "🚨 #{problematic.count} campañas problemáticas: #{problematic.pluck(:status).uniq.join(', ')}"
    end
    
    # Recomendación
    if kafka_adoption < 50
      puts "🔴 CRÍTICO: Adopción muy baja (#{kafka_adoption}%)"
    elsif kafka_adoption < 80
      puts "🟡 MEJORABLE: Adopción moderada (#{kafka_adoption}%)"
    else
      puts "🟢 BIEN: Buena adopción (#{kafka_adoption}%)"
    end
  end
  
  def run_detailed_analysis
    print_header
    retailer_ids.each_with_index do |retailer_id, index|
      analyze_retailer(retailer_id, index)
    end
  end
end
