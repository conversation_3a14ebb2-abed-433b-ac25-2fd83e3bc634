class KafkaSummaryReporter
  attr_reader :start_date, :retailer_ids
  
  def initialize(start_date = '2025-07-31', retailer_ids = [])
    @start_date = start_date
    @retailer_ids = retailer_ids
  end
  
  def quick_summary
    puts "\n📈 RESUMEN RÁPIDO DESDE 01/08:"
    retailer_ids.each do |id|
      retailer = Retailer.find_by(id: id)
      next unless retailer&.kafka_enabled
      
      campaigns = retailer.campaigns.where('created_at >= ?', start_date)
      next if campaigns.empty?
      
      kafka_count = campaigns.where(sender_strategy: 'kafka').count
      processing_count = campaigns.where(status: 'processing').count
      adoption = (kafka_count.to_f / campaigns.count * 100).round(1)
      
      icon = adoption >= 80 ? "🟢" : adoption >= 50 ? "🟡" : "🔴"
      processing_icon = processing_count > 0 ? " 🔄#{processing_count}" : ""
      puts "#{icon} #{retailer.name}: #{adoption}% (#{kafka_count}/#{campaigns.count})#{processing_icon}"
    end
  end
  
  def processing_campaigns
    puts "\n🎯 CAMPAÑAS EN PROCESSING DESDE 31/07:"
    Campaign.joins(:retailer)
            .where('campaigns.created_at >= ?', start_date)
            .where(status: 'processing')
            .where(retailers: { id: retailer_ids })
            .each do |c|
      puts "🔄 #{c.retailer.name} | #{c.id} | #{c.web_id} | #{c.name[0..40]}... | #{c.created_at.strftime('%d/%m %H:%M')}"
    end
  end
  
  def global_statistics
    puts "\n" + "=" * 70
    puts "📊 ESTADÍSTICAS GLOBALES DEL SISTEMA"
    puts "=" * 70

    # Todas las campañas del período
    all_campaigns_period = Campaign.where('created_at >= ?', start_date)
    total_campaigns_system = all_campaigns_period.count

    # Campañas por estrategia en todo el sistema
    kafka_strategy_total = all_campaigns_period.where(sender_strategy: 'kafka').count
    sync_strategy_total = all_campaigns_period.where(sender_strategy: 'synchronous').count
    nil_strategy_total = all_campaigns_period.where(sender_strategy: nil).count

    # Porcentajes globales
    kafka_pct_global = (kafka_strategy_total.to_f / total_campaigns_system * 100).round(1)
    sync_pct_global = (sync_strategy_total.to_f / total_campaigns_system * 100).round(1)
    nil_pct_global = (nil_strategy_total.to_f / total_campaigns_system * 100).round(1)

    puts "🌍 DISTRIBUCIÓN GLOBAL DE ESTRATEGIAS (#{total_campaigns_system} campañas totales):"
    puts "   ✅ Kafka: #{kafka_pct_global}% (#{kafka_strategy_total} campañas)"
    puts "   ⚠️  Sync:  #{sync_pct_global}% (#{sync_strategy_total} campañas)"
    puts "   ❓ Nil:   #{nil_pct_global}% (#{nil_strategy_total} campañas)"

    fallback_analysis
    retailer_analysis
    effectiveness_analysis(all_campaigns_period, kafka_strategy_total, sync_strategy_total, nil_strategy_total, total_campaigns_system, kafka_pct_global)
  end
  
  def fallback_analysis
    # Análisis de fallback: retailers con kafka_enabled que usaron sync/nil
    fallback_campaigns = Campaign.joins(:retailer)
                                .where('campaigns.created_at >= ?', start_date)
                                .where(retailers: { kafka_enabled: true })
                                .where(sender_strategy: ['synchronous', nil])

    kafka_enabled_campaigns = Campaign.joins(:retailer)
                                     .where('campaigns.created_at >= ?', start_date)
                                     .where(retailers: { kafka_enabled: true })

    fallback_count = fallback_campaigns.count
    kafka_enabled_total = kafka_enabled_campaigns.count
    fallback_pct = kafka_enabled_total > 0 ? (fallback_count.to_f / kafka_enabled_total * 100).round(1) : 0

    puts "\n🚨 ANÁLISIS DE FALLBACK:"
    puts "   📊 Retailers con kafka_enabled enviaron: #{kafka_enabled_total} campañas"
    puts "   ⚠️  Fallback (sync/nil en retailers con kafka): #{fallback_pct}% (#{fallback_count} campañas)"
    puts "   ✅ Realmente usaron Kafka: #{((kafka_enabled_total - fallback_count).to_f / kafka_enabled_total * 100).round(1)}%"
  end
  
  def retailer_analysis
    # Retailers activos en el período
    active_retailers_total = Retailer.joins(:campaigns)
                                   .where('campaigns.created_at >= ?', start_date)
                                   .distinct
                                   .count

    active_retailers_kafka_enabled = Retailer.joins(:campaigns)
                                            .where('campaigns.created_at >= ?', start_date)
                                            .where(kafka_enabled: true)
                                            .distinct
                                            .count

    active_retailers_kafka_disabled = active_retailers_total - active_retailers_kafka_enabled

    puts "\n🏪 RETAILERS ACTIVOS EN EL PERÍODO:"
    puts "   📊 Total retailers que enviaron campañas: #{active_retailers_total}"
    puts "   ✅ Con kafka_enabled=true: #{active_retailers_kafka_enabled} (#{(active_retailers_kafka_enabled.to_f / active_retailers_total * 100).round(1)}%)"
    puts "   ❌ Con kafka_enabled=false: #{active_retailers_kafka_disabled} (#{(active_retailers_kafka_disabled.to_f / active_retailers_total * 100).round(1)}%)"
  end
  
  def effectiveness_analysis(all_campaigns_period, kafka_strategy_total, sync_strategy_total, nil_strategy_total, total_campaigns_system, kafka_pct_global)
    # Efectividad por estrategia
    kafka_sent = all_campaigns_period.where(sender_strategy: 'kafka', status: 'sent').count
    sync_sent = all_campaigns_period.where(sender_strategy: 'synchronous', status: 'sent').count
    nil_sent = all_campaigns_period.where(sender_strategy: nil, status: 'sent').count

    puts "\n🎯 EFECTIVIDAD POR ESTRATEGIA:"
    if kafka_strategy_total > 0
      kafka_effectiveness = (kafka_sent.to_f / kafka_strategy_total * 100).round(1)
      puts "   ✅ Kafka: #{kafka_effectiveness}% (#{kafka_sent}/#{kafka_strategy_total} enviadas)"
    end

    if sync_strategy_total > 0
      sync_effectiveness = (sync_sent.to_f / sync_strategy_total * 100).round(1)
      puts "   ⚠️  Sync:  #{sync_effectiveness}% (#{sync_sent}/#{sync_strategy_total} enviadas)"
    end

    if nil_strategy_total > 0
      nil_effectiveness = (nil_sent.to_f / nil_strategy_total * 100).round(1)
      puts "   ❓ Nil:   #{nil_effectiveness}% (#{nil_sent}/#{nil_strategy_total} enviadas)"
    end

    puts "\n🏁 ANÁLISIS GLOBAL COMPLETADO"
    puts "📊 Resumen: #{total_campaigns_system} campañas, retailers activos, #{kafka_pct_global}% adopción real Kafka"
  end
  
  def run_all_summaries
    quick_summary
    processing_campaigns
    global_statistics
    puts "\n🏁 ANÁLISIS COMPLETADO"
  end
end
