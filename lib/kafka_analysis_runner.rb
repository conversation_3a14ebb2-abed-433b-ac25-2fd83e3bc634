require_relative 'kafka_campaign_analyzer'
require_relative 'kafka_summary_reporter'

class KafkaAnalysisRunner
  def self.run_full_analysis(start_date = '2025-07-31')
    puts "🚀 Iniciando análisis completo de Kafka..."
    
    # Paso 1: Análisis detallado
    analyzer = KafkaCampaignAnalyzer.new(start_date)
    analyzer.run_detailed_analysis
    
    # Paso 2: Resúmenes y estadísticas
    reporter = KafkaSummaryReporter.new(start_date, analyzer.retailer_ids)
    reporter.run_all_summaries
    
    puts "✅ Análisis completo terminado!"
  end
  
  def self.run_quick_summary(start_date = '2025-07-31')
    puts "⚡ Ejecutando resumen rápido..."
    
    analyzer = KafkaCampaignAnalyzer.new(start_date)
    reporter = KafkaSummaryReporter.new(start_date, analyzer.retailer_ids)
    
    analyzer.print_header
    reporter.quick_summary
    reporter.global_statistics
    
    puts "✅ Resumen rápido completado!"
  end
  
  def self.run_detailed_only(start_date = '2025-07-31')
    puts "🔍 Ejecutando solo análisis detallado..."
    
    analyzer = KafkaCampaignAnalyzer.new(start_date)
    analyzer.run_detailed_analysis
    
    puts "✅ Análisis detallado completado!"
  end
  
  def self.run_statistics_only(start_date = '2025-07-31')
    puts "📊 Ejecutando solo estadísticas globales..."
    
    analyzer = KafkaCampaignAnalyzer.new(start_date)
    reporter = KafkaSummaryReporter.new(start_date, analyzer.retailer_ids)
    
    reporter.global_statistics
    
    puts "✅ Estadísticas globales completadas!"
  end
end
