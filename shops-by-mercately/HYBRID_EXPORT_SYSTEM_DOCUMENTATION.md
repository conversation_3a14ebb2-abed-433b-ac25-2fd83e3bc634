# 🔄 Sistema Híbrido de Exports - Documentación Técnica

## 📋 Resumen Ejecutivo

El **Sistema Híbrido de Exports** combina webhooks en tiempo real con polling de fallback para proporcionar una experiencia de usuario óptima en las exportaciones de órdenes, eliminando la necesidad de polling constante mientras mantiene la confiabilidad del sistema.

## 🎯 Objetivos Alcanzados

- ✅ **Eliminar polling constante** - Reduce carga del servidor
- ✅ **Notificaciones en tiempo real** - Webhooks + ActionCable
- ✅ **Fallback confiable** - Polling automático si webhooks fallan
- ✅ **UX mejorada** - Feedback inmediato al usuario
- ✅ **Logs completos** - Debugging y monitoreo

## 🏗️ Arquitectura del Sistema

```
┌─────────────────┐    webhook     ┌─────────────────┐    ActionCable    ┌─────────────────┐
│     SHOPS       │───────────────▶│   MERCATELY     │──────────────────▶│    FRONTEND     │
│                 │                │                 │                   │                 │
│ - Jobs          │                │ - WebhookCtrl   │                   │ - React/Redux   │
│ - WebhookSvc    │                │ - ExportsChannel│                   │ - Polling Logic │
│ - ExportStatus  │                │ - ActionCable   │                   │ - UI Updates    │
└─────────────────┘                └─────────────────┘                   └─────────────────┘
        │                                   │                                      │
        │                                   │                                      │
        └─── Si webhook falla ──────────────┴──── Polling Fallback ──────────────┘
```

## 🔄 Flujo de Funcionamiento

### **Flujo Principal (Webhooks)**

1. **Usuario inicia export** en Mercately
2. **Request enviado** a Shops API
3. **Job encolado** en Sidekiq/DelayedJob
4. **Frontend registra** tiempo de inicio y se suscribe a ActionCable
5. **Job procesa** la exportación
6. **Job termina** → WebhookService envía notificación a Mercately
7. **Mercately recibe** webhook → valida token → broadcast ActionCable
8. **Frontend recibe** notificación → actualiza Redux → UI actualizada

### **Flujo de Fallback (Polling)**

Si el webhook no llega en **30 segundos**:

1. **Timeout activado** → webhookTimeout = true
2. **Polling iniciado** cada 5 segundos
3. **Estado verificado** via API hasta completar
4. **UI actualizada** cuando se detecta cambio

## 📁 Estructura de Archivos

### **Mercately (Frontend + Backend)**

```
mercately_rails6/
├── app/
│   ├── controllers/api/v1/
│   │   └── webhooks_controller.rb          # Recibe webhooks de Shops
│   ├── channels/
│   │   └── exports_channel.rb              # ActionCable para tiempo real
│   └── javascript/
│       ├── channels/
│       │   └── exports_channel.js          # Manager de ActionCable
│       ├── components/Orders/
│       │   └── index.js                    # Componente con lógica híbrida
│       ├── actions/shops/
│       │   └── orders.js                   # Actions de Redux
│       └── reducers/
│           └── mainReducer.js              # Reducer con webhook support
```

### **Shops (Backend)**

```
shops-by-mercately/
├── app/
│   ├── services/
│   │   └── webhook_service.rb              # Envía webhooks a Mercately
│   ├── jobs/exports/orders/
│   │   ├── detailed_records_job.rb         # Job con webhook integration
│   │   └── records_job.rb                  # Job con webhook integration
│   └── models/
│       └── export_status.rb               # Tracking de exports
```

## 🔧 Componentes Técnicos

### **1. WebhookService (Shops)**

**Ubicación:** `app/services/webhook_service.rb`

**Responsabilidades:**
- Enviar notificaciones HTTP a Mercately
- Manejo de errores y timeouts
- Logs detallados para debugging

**Métodos principales:**
```ruby
WebhookService.send_export_completed(
  retailer_id: 123,
  export_type: 'detailed', # 'summary' o 'detailed'
  status: 'completed',     # 'completed' o 'failed'
  download_url: 'https://...',
  error_message: nil
)
```

### **2. WebhooksController (Mercately)**

**Ubicación:** `app/controllers/api/v1/webhooks_controller.rb`

**Endpoint:** `POST /api/v1/webhooks/export_completed`

**Responsabilidades:**
- Recibir y validar webhooks de Shops
- Verificar token de seguridad
- Broadcast via ActionCable al frontend

**Headers requeridos:**
```
X-Webhook-Token: mercately_webhook_secret_2024
Content-Type: application/json
```

### **3. ExportsChannel (Mercately)**

**Ubicación:** `app/channels/exports_channel.rb`

**Responsabilidades:**
- Manejar conexiones WebSocket
- Broadcast notificaciones a usuarios específicos
- Logs de conexiones y desconexiones

**Suscripción:**
```javascript
exportsChannelManager.subscribe(retailerId, {
  onExportStatusUpdate: (status) => { /* actualizar Redux */ }
});
```

### **4. Lógica Híbrida Frontend**

**Ubicación:** `app/javascript/components/Orders/index.js`

**Estados clave:**
```javascript
const [webhookTimeout, setWebhookTimeout] = useState(false);
const [exportStartTime, setExportStartTime] = useState(null);
const [pollingActive, setPollingActive] = useState(false);
```

**Condiciones de polling:**
```javascript
const shouldUsePollingFallback = () => {
  return webhookTimeout ||           // Webhook no llegó en 30s
         exportTakingTooLong ||      // Export > 2 minutos
         !exportStartTime;           // Usuario reconectó
};
```

## ⚙️ Configuración

### **Variables de Entorno**

**Shops:**
```bash
MERCATELY_BASE_URL=https://app.mercately.com
SHOPS_WEBHOOK_TOKEN=mercately_webhook_secret_2024
```

**Mercately:**
```bash
SHOPS_WEBHOOK_TOKEN=mercately_webhook_secret_2024
RETAILER_ID=123  # Disponible en ENV del frontend
```

### **Rutas**

**Mercately:**
```ruby
# config/routes.rb
namespace :api, defaults: { format: :json } do
  namespace :v1 do
    post 'webhooks/export_completed', to: 'webhooks#export_completed'
  end
end
```

## 📊 Estados y Transiciones

### **Estados de Export**

```javascript
exportStatus = {
  exporting: false,
  summary: {
    in_progress: false,
    ready: false,
    download_url: null,
    error: null
  },
  detailed: {
    in_progress: false,
    ready: false,
    download_url: null,
    error: null
  }
}
```

### **Transiciones de Estado**

1. **Inicio:** `exporting: true, [type]: { in_progress: true }`
2. **Webhook recibido:** `exporting: false, [type]: { ready: true, download_url: "..." }`
3. **Error:** `exporting: false, [type]: { error: "mensaje" }`
4. **Timeout:** `webhookTimeout: true` → activa polling

## 🔍 Logs y Debugging

### **Identificadores de Log**

- `🔔 [WEBHOOK SENDER]` - Envío desde Shops
- `🔔 [WEBHOOK]` - Recepción en Mercately
- `📡 [ACTIONCABLE]` - Comunicación WebSocket
- `🔄 [FALLBACK POLLING]` - Polling activado
- `⏰ [WEBHOOK TIMEOUT]` - Timeout de webhook

### **Logs Importantes**

**Shops (Envío):**
```
✅ [DETAILED-JOB] Exportación completada exitosamente para retailer 123
🔔 [WEBHOOK SENDER] Enviando webhook a Mercately
✅ [WEBHOOK SENDER] Webhook enviado exitosamente
```

**Mercately (Recepción):**
```
🔔 [WEBHOOK] Recibido webhook de export completado
✅ [WEBHOOK] Notificación enviada via ActionCable a exports_123
```

**Frontend (Procesamiento):**
```
📡 [ACTIONCABLE] Suscribiéndose a notificaciones de export...
🔔 [ACTIONCABLE] Webhook recibido, actualizando Redux
🔄 [FALLBACK POLLING] Activando polling fallback (si timeout)
```

## 🚨 Manejo de Errores

### **Escenarios de Error**

1. **Webhook falla** → Polling fallback automático
2. **ActionCable desconectado** → Reconexión automática
3. **Export falla** → Webhook de error + UI actualizada
4. **Timeout de red** → Logs de error + fallback

### **Recuperación Automática**

- **Webhook timeout:** 30 segundos → polling cada 5s
- **Export largo:** >2 minutos → polling automático
- **Reconexión:** Usuario vuelve → polling si export activo
- **Error de webhook:** Logs + polling continúa funcionando

## 📈 Beneficios del Sistema

### **Performance**
- ❌ **Antes:** Polling cada 10s siempre activo
- ✅ **Ahora:** Webhooks instantáneos + polling solo cuando necesario

### **UX**
- ❌ **Antes:** Hasta 10s de delay para ver cambios
- ✅ **Ahora:** Notificación instantánea + fallback confiable

### **Confiabilidad**
- ❌ **Antes:** Solo polling (punto único de falla)
- ✅ **Ahora:** Doble sistema (webhook + polling fallback)

### **Debugging**
- ❌ **Antes:** Logs básicos
- ✅ **Ahora:** Logs detallados en cada paso del flujo

## 🔮 Próximos Pasos

1. **Monitoreo:** Implementar métricas de webhooks exitosos/fallidos
2. **Optimización:** Ajustar timeouts basado en datos reales
3. **Escalabilidad:** Considerar Redis para ActionCable en múltiples servidores
4. **Testing:** Agregar tests automatizados del flujo completo

## 🚀 Guía Rápida para Desarrolladores

### **Testing Local**

1. **Configurar variables de entorno:**
```bash
# En Shops
export MERCATELY_BASE_URL=http://localhost:3000
export SHOPS_WEBHOOK_TOKEN=test_token_123

# En Mercately
export SHOPS_WEBHOOK_TOKEN=test_token_123
```

2. **Probar webhook manualmente:**
```bash
curl -X POST http://localhost:3000/api/v1/webhooks/export_completed \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Token: test_token_123" \
  -d '{
    "retailer_id": 123,
    "export_type": "detailed",
    "status": "completed",
    "download_url": "https://example.com/file.xlsx"
  }'
```

3. **Verificar logs:**
```bash
# Shops
tail -f log/development.log | grep WEBHOOK

# Mercately
tail -f log/development.log | grep -E "WEBHOOK|ACTIONCABLE"
```

### **Debugging Común**

**Problema:** Webhook no llega
```bash
# Verificar en Shops
grep "WEBHOOK SENDER" log/development.log

# Verificar en Mercately
grep "WEBHOOK.*Recibido" log/development.log
```

**Problema:** ActionCable no conecta
```javascript
// En browser console
exportsChannelManager.ping(); // Debe responder con pong
```

**Problema:** Polling no se activa
```javascript
// En browser console
console.log('Webhook timeout:', webhookTimeout);
console.log('Export start time:', exportStartTime);
```

### **Modificaciones Futuras**

**Agregar nuevo tipo de export:**
1. Modificar `WebhookService` para nuevo tipo
2. Agregar caso en `WebhooksController`
3. Actualizar frontend para manejar nuevo estado

**Cambiar timeouts:**
```javascript
// En Orders/index.js
const WEBHOOK_TIMEOUT = 45000; // 45 segundos
const POLLING_INTERVAL = 3000;  // 3 segundos
const EXPORT_LONG_TIME = 180000; // 3 minutos
```

---

**Implementado en:** MER-2114
**Fecha:** Agosto 2024
**Desarrollador:** Sistema híbrido completo
**Estado:** ✅ Producción Ready
