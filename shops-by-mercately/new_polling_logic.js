  // Función para determinar si debe usar polling fallback
  const shouldUsePollingFallback = () => {
    const isExportingNow = exportStatus.exporting || 
                          exportStatus.summary?.in_progress || 
                          exportStatus.detailed?.in_progress;
    
    if (!isExportingNow) return false;
    
    // Activar polling si:
    // 1. Webhook timeout (no llegó webhook en 30 segundos)
    // 2. Export lleva más de 2 minutos
    // 3. Usuario reconectó con export activo
    const exportTakingTooLong = exportStartTime && 
                               (Date.now() - exportStartTime) > 120000; // 2 minutos
    
    return webhookTimeout || exportTakingTooLong || !exportStartTime;
  };

  // Polling condicional (solo como fallback)
  useEffect(() => {
    let interval = null;
    
    if (shouldUsePollingFallback()) {
      console.log('🔄 [FALLBACK POLLING] Activando polling fallback');
      setPollingActive(true);
      
      const checkExportStatus = () => {
        console.log('🔄 [FALLBACK POLLING] Verificando estado...');
        dispatch(fetchExportStatus());
      };

      checkExportStatus(); // Llamada inicial
      interval = setInterval(checkExportStatus, 5000); // Cada 5 segundos (más frecuente)
    } else {
      console.log('🚫 [FALLBACK POLLING] Polling desactivado - esperando webhooks');
      setPollingActive(false);
    }

    return () => {
      if (interval) {
        console.log('🔄 [FALLBACK POLLING] Limpiando polling...');
        clearInterval(interval);
      }
    };
  }, [dispatch, webhookTimeout, exportStartTime, exportStatus]);

  // Timeout para webhook - activar polling si no llega webhook
  useEffect(() => {
    let timeoutId = null;
    
    const isExportingNow = exportStatus.exporting || 
                          exportStatus.summary?.in_progress || 
                          exportStatus.detailed?.in_progress;
    
    if (isExportingNow && !webhookTimeout && exportStartTime) {
      console.log('⏰ [WEBHOOK TIMEOUT] Configurando timeout de 30 segundos...');
      
      timeoutId = setTimeout(() => {
        console.log('⏰ [WEBHOOK TIMEOUT] Webhook no llegó - activando polling fallback');
        setWebhookTimeout(true);
      }, 30000); // 30 segundos
    }
    
    // Si export terminó, resetear timeout
    if (!isExportingNow && webhookTimeout) {
      console.log('✅ [WEBHOOK TIMEOUT] Export terminado - reseteando timeout');
      setWebhookTimeout(false);
      setExportStartTime(null);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [exportStatus, webhookTimeout, exportStartTime]);
