#!/usr/bin/env ruby
# Script para modificar el componente Orders y convertirlo a sistema híbrido

def modify_orders_component
  file_path = 'app/javascript/components/Orders/index.js'
  
  # Leer el archivo
  content = File.read(file_path)
  puts "🔍 Archivo leído exitosamente"
  
  # 1. Reemplazar el bloque del polling antiguo
  old_polling_pattern = /  \/\/ Redux-based polling para export status\s*useEffect\(\(\) => \{.*?\}, \[dispatch\]\);/m
  
  new_polling_code = <<~JS
    // Función para determinar si debe usar polling fallback
    const shouldUsePollingFallback = () => {
      const isExportingNow = exportStatus.exporting || 
                            exportStatus.summary?.in_progress || 
                            exportStatus.detailed?.in_progress;
      
      if (!isExportingNow) return false;
      
      // Activar polling si:
      // 1. Webhook timeout (no llegó webhook en 30 segundos)
      // 2. Export lleva más de 2 minutos
      // 3. Usuario reconectó con export activo
      const exportTakingTooLong = exportStartTime && 
                                 (Date.now() - exportStartTime) > 120000; // 2 minutos
      
      return webhookTimeout || exportTakingTooLong || !exportStartTime;
    };

    // Polling condicional (solo como fallback)
    useEffect(() => {
      let interval = null;
      
      if (shouldUsePollingFallback()) {
        console.log('🔄 [FALLBACK POLLING] Activando polling fallback');
        setPollingActive(true);
        
        const checkExportStatus = () => {
          console.log('🔄 [FALLBACK POLLING] Verificando estado...');
          dispatch(fetchExportStatus());
        };

        checkExportStatus(); // Llamada inicial
        interval = setInterval(checkExportStatus, 5000); // Cada 5 segundos (más frecuente)
      } else {
        console.log('🚫 [FALLBACK POLLING] Polling desactivado - esperando webhooks');
        setPollingActive(false);
      }

      return () => {
        if (interval) {
          console.log('🔄 [FALLBACK POLLING] Limpiando polling...');
          clearInterval(interval);
        }
      };
    }, [dispatch, webhookTimeout, exportStartTime, exportStatus]);

    // Timeout para webhook - activar polling si no llega webhook
    useEffect(() => {
      let timeoutId = null;
      
      const isExportingNow = exportStatus.exporting || 
                            exportStatus.summary?.in_progress || 
                            exportStatus.detailed?.in_progress;
      
      if (isExportingNow && !webhookTimeout && exportStartTime) {
        console.log('⏰ [WEBHOOK TIMEOUT] Configurando timeout de 30 segundos...');
        
        timeoutId = setTimeout(() => {
          console.log('⏰ [WEBHOOK TIMEOUT] Webhook no llegó - activando polling fallback');
          setWebhookTimeout(true);
        }, 30000); // 30 segundos
      }
      
      // Si export terminó, resetear timeout
      if (!isExportingNow && webhookTimeout) {
        console.log('✅ [WEBHOOK TIMEOUT] Export terminado - reseteando timeout');
        setWebhookTimeout(false);
        setExportStartTime(null);
      }

      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      };
    }, [exportStatus, webhookTimeout, exportStartTime]);
  JS
  
  # Hacer el reemplazo
  content = content.gsub(old_polling_pattern, new_polling_code.strip)
  puts "✅ Polling híbrido reemplazado"
  
  # 2. Modificar las funciones de export para registrar tiempo de inicio
  # Export normal
  content = content.gsub(
    /(const exportFilteredOrder = \(\) => \{[^}]*if \(confirm\([^)]*\)\) \{[^}]*console\.log\([^)]*\);)\s*(dispatch\(startOrderExport\(search\)\);)/m,
    "\\1\n      setExportStartTime(Date.now()); // Registrar tiempo de inicio\n      setWebhookTimeout(false); // Reset timeout\n      \\2"
  )
  puts "✅ Función exportFilteredOrder modificada"
  
  # Export detallado
  content = content.gsub(
    /(const exportFilteredDetailedOrder = \(\) => \{[^}]*if \(confirm\([^)]*\)\) \{[^}]*console\.log\([^)]*\);)\s*(dispatch\(startDetailedOrderExport\(search\)\);)/m,
    "\\1\n      setExportStartTime(Date.now()); // Registrar tiempo de inicio\n      setWebhookTimeout(false); // Reset timeout\n      \\2"
  )
  puts "✅ Función exportFilteredDetailedOrder modificada"
  
  # 3. Modificar el texto del botón para mostrar estado de polling
  old_button_text = /const getExportButtonText = \(\) => \{\s*if \(isExporting\(\)\) return "Exportando\.\.\.";\s*if \(hasReadyDownload\(\)\) return "Descargar";\s*return "Exportar";\s*\};/m
  new_button_text = <<~JS.strip
    const getExportButtonText = () => {
      if (isExporting()) {
        return pollingActive ? "Exportando... (verificando)" : "Exportando...";
      }
      if (hasReadyDownload()) return "Descargar";
      return "Exportar";
    };
  JS
  content = content.gsub(old_button_text, new_button_text)
  puts "✅ Texto del botón modificado"
  
  # 4. Agregar logs adicionales al useEffect de exportStatus
  old_log = /\/\/ Log cuando cambie el exportStatus\s*useEffect\(\(\) => \{\s*console\.log\([^)]*exportStatus\);\s*\}, \[exportStatus\]\);/m
  new_log = <<~JS.strip
    // Log cuando cambie el exportStatus
    useEffect(() => {
      console.log('📊 [REDUX STATE] Export status updated:', exportStatus);
      console.log('🔄 [POLLING STATE] Polling active:', pollingActive);
      console.log('⏰ [WEBHOOK STATE] Webhook timeout:', webhookTimeout);
    }, [exportStatus, pollingActive, webhookTimeout]);
  JS
  content = content.gsub(old_log, new_log)
  puts "✅ Logs adicionales agregados"
  
  # Escribir el archivo modificado
  File.write(file_path, content)
  
  puts "🎉 ¡Archivo modificado exitosamente!"
  puts "📋 Cambios realizados:"
  puts "   - ✅ Polling convertido a sistema híbrido"
  puts "   - ✅ Funciones de export registran tiempo de inicio"
  puts "   - ✅ Botón muestra estado de polling"
  puts "   - ✅ Logs mejorados para debugging"
end

# Ejecutar el script
modify_orders_component
