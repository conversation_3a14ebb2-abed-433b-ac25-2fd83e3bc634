/* eslint-disable import/no-unresolved */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import ReactPaginate from 'react-paginate';
import { isEmpty } from 'lodash';
import ZoomIcon from 'images/zoom.svg';
import FilterIcon from 'images/filter.svg';

import { fetchOrders, getPendingOrder, fetchExportStatus, startOrderExport, startDetailedOrderExport } from '../../actions/shops/orders';
import { getCurrentRetailerUserInfo, getRetailerInfo } from '../../actions/retailerUsersActions';

import SidebarModal from '../shared/SidebarModal';
import FilterOrdersForm from './FilterOrdersForm';
import SidebarStore from '../shared/SidebarStore';
import httpServiceShops from '../../services/httpServiceShops';
import SidebarStoreContent from '../shared/SidebarStore/content/SidebarStoreContent';
import OrderTableList from './OrderTableList';

const Orders = () => {
  console.log('🚀 [FRONTEND] Orders component loaded - HYBRID SYSTEM!');
  const dispatch = useDispatch();

  // Estados para el sistema híbrido
  const [webhookTimeout, setWebhookTimeout] = useState(false);
  const [exportStartTime, setExportStartTime] = useState(null);
  const [pollingActive, setPollingActive] = useState(false);

  const {
    orders = [],
    total_pages = 0,
    orderLoading,
    exportStatus = { exporting: false, summary: {}, detailed: {} }
  } = useSelector((reduxState) => reduxState.mainReducer);
  const { totalPendindOrders } = useSelector((reduxState) => reduxState.ordersReducer);

  // Función para determinar si debe usar polling fallback
  const shouldUsePollingFallback = () => {
    const isExportingNow = exportStatus.exporting || 
                          exportStatus.summary?.in_progress || 
                          exportStatus.detailed?.in_progress;
    
    if (!isExportingNow) return false;
    
    // Activar polling si:
    // 1. Webhook timeout (no llegó webhook en 30 segundos)
    // 2. Export lleva más de 2 minutos
    // 3. Usuario reconectó con export activo
    const exportTakingTooLong = exportStartTime && 
                               (Date.now() - exportStartTime) > 120000; // 2 minutos
    
    return webhookTimeout || exportTakingTooLong || !exportStartTime;
  };

  // Polling condicional (solo como fallback)
  useEffect(() => {
    let interval = null;
    
    if (shouldUsePollingFallback()) {
      console.log('🔄 [FALLBACK POLLING] Activando polling fallback');
      setPollingActive(true);
      
      const checkExportStatus = () => {
        console.log('🔄 [FALLBACK POLLING] Verificando estado...');
        dispatch(fetchExportStatus());
      };

      checkExportStatus(); // Llamada inicial
      interval = setInterval(checkExportStatus, 5000); // Cada 5 segundos (más frecuente)
    } else {
      console.log('🚫 [FALLBACK POLLING] Polling desactivado - esperando webhooks');
      setPollingActive(false);
    }

    return () => {
      if (interval) {
        console.log('🔄 [FALLBACK POLLING] Limpiando polling...');
        clearInterval(interval);
      }
    };
  }, [dispatch, webhookTimeout, exportStartTime, exportStatus]);

  // Timeout para webhook - activar polling si no llega webhook
  useEffect(() => {
    let timeoutId = null;
    
    const isExportingNow = exportStatus.exporting || 
                          exportStatus.summary?.in_progress || 
                          exportStatus.detailed?.in_progress;
    
    if (isExportingNow && !webhookTimeout && exportStartTime) {
      console.log('⏰ [WEBHOOK TIMEOUT] Configurando timeout de 30 segundos...');
      
      timeoutId = setTimeout(() => {
        console.log('⏰ [WEBHOOK TIMEOUT] Webhook no llegó - activando polling fallback');
        setWebhookTimeout(true);
      }, 30000); // 30 segundos
    }
    
    // Si export terminó, resetear timeout
    if (!isExportingNow && webhookTimeout) {
      console.log('✅ [WEBHOOK TIMEOUT] Export terminado - reseteando timeout');
      setWebhookTimeout(false);
      setExportStartTime(null);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [exportStatus, webhookTimeout, exportStartTime]);

  // Log cuando cambie el exportStatus
  useEffect(() => {
    console.log('📊 [REDUX STATE] Export status updated:', exportStatus);
    console.log('🔄 [POLLING STATE] Polling active:', pollingActive);
    console.log('⏰ [WEBHOOK STATE] Webhook timeout:', webhookTimeout);
  }, [exportStatus, pollingActive, webhookTimeout]);

  // Helper functions para el estado del botón de exportar
  const isExporting = () => {
    return exportStatus.exporting ||
           exportStatus.summary?.in_progress ||
           exportStatus.detailed?.in_progress;
  };

  const hasReadyDownload = () => {
    return exportStatus.summary?.ready || exportStatus.detailed?.ready;
  };

  const getExportButtonText = () => {
    if (isExporting()) {
      return pollingActive ? "Exportando... (verificando)" : "Exportando...";
    }
    if (hasReadyDownload()) return "Descargar";
    return "Exportar";
  };

  const getExportButtonIcon = () => {
    if (isExporting()) return "fas fa-spinner fa-spin ml-8";
    if (hasReadyDownload()) return "fas fa-download ml-8";
    return "fas fa-caret-down ml-8";
  };

  const isExportDisabled = () => {
    return isExporting();
  };

  const [page, setPage] = useState(0);
  const [showSection, setShowSection] = useState('orders');
  const [searchText, setSearchText] = useState('');
  const [search, setSearch] = useState({});
  const reloadOrderRef = useRef(false);

  // Funciones de export modificadas para registrar tiempo de inicio
  const exportFilteredOrder = () => {
    if (isExportDisabled()) return; // Prevenir clicks durante export

    if (confirm('¿Estás seguro de exportar órdenes con los filtros seleccionados?')) {
      console.log('🚀 [EXPORT] Starting summary export...');
      setExportStartTime(Date.now()); // Registrar tiempo de inicio
      setWebhookTimeout(false); // Reset timeout
      dispatch(startOrderExport(search));
    }
  };

  const exportFilteredDetailedOrder = () => {
    if (isExportDisabled()) return; // Prevenir clicks durante export

    if (confirm('¿Estás seguro de exportar detalle de órdenes con los filtros seleccionados?')) {
      console.log('🚀 [EXPORT] Starting detailed export...');
      setExportStartTime(Date.now()); // Registrar tiempo de inicio
      setWebhookTimeout(false); // Reset timeout
      dispatch(startDetailedOrderExport(search));
    }
  };

  useEffect(() => {
    reloadOrderRef.current = false;
    if (searchText === '') {
      reloadOrderRef.current = true;
      searchOrder();
    }
  }, [searchText]);

  useEffect(() => {
    if (!isEmpty(orders)) {
      dispatch(getPendingOrder());
    }
  }, [orders]);

  const sectionToShow = () => {
    switch (showSection) {
      case 'successful_orders':
        return 2;
      case 'pending_orders':
        return 1;
      default:
        return 0;
    }
  };

  const searchOrder = () => {
    dispatch(fetchOrders(page, search, sectionToShow()));
  };

  const handleSearch = (searchParams) => {
    setSearch(searchParams);
    setPage(0);
    dispatch(fetchOrders(0, searchParams, sectionToShow()));
  };

  const handlePageClick = (event) => {
    const newPage = event.selected + 1;
    setPage(newPage);
    dispatch(fetchOrders(newPage, search, sectionToShow()));
  };

  const handleSearchText = (e) => {
    setSearchText(e.target.value);
    if (e.target.value !== '') {
      const searchParams = { ...search, customer_full_name_cont: e.target.value };
      setSearch(searchParams);
      setPage(0);
      dispatch(fetchOrders(0, searchParams, sectionToShow()));
    }
  };

  const handleShowSection = (section) => {
    setShowSection(section);
    setPage(0);
    dispatch(fetchOrders(0, search, sectionToShow()));
  };

  useEffect(() => {
    if (reloadOrderRef.current) {
      searchOrder();
    }
  }, [showSection]);

  useEffect(() => {
    dispatch(getCurrentRetailerUserInfo());
    dispatch(getRetailerInfo());
    searchOrder();
  }, []);

  return (
    <div className="ml-sm-61 mr-sm-10 no-left-margin-xs">
      <SidebarModal id="order_filters" title="Filtros">
        <FilterOrdersForm handleSearch={handleSearch} />
      </SidebarModal>
      <div className="container-fluid-no-padding">
        <div className="row">
          <div className="d-flex">
            <SidebarStore>
              <SidebarStoreContent place="orders" />
            </SidebarStore>
          </div>
          <div className="col bg-white px-24 store-content pt-10">
            <div className="row align-items-center">
              <div className="col col-sm-4 mt-12">
                <h1 className="title-page">Órdenes</h1>
              </div>
              <div className="col col-sm-8 d-flex justify-content-end align-items-center">
                <div className="dropdown">
                  <button
                    className={`btn dropdown-toggle d-flex align-items-center ${isExportDisabled() ? 'btn-secondary' : 'btn-outline-primary'}`}
                    type="button"
                    id="dropdownMenuButton"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                    disabled={isExportDisabled()}
                  >
                    {getExportButtonText()}
                    <i className={getExportButtonIcon()}></i>
                  </button>
                  {!isExportDisabled() && (
                    <ul className="dropdown-menu" aria-labelledby="dropdownMenuButton">
                      <li className="t-left" onClick={exportFilteredOrder}>
                        Resumen de Órdenes a Excel
                      </li>
                      <li className="t-left" onClick={exportFilteredDetailedOrder} style={{ whiteSpace: "break-spaces" }}>
                        Detalle de Órdenes con productos a Excel
                      </li>
                    </ul>
                  )}
                </div>
                <div className="d-inline">
                  <Link className="blue-button my-24px my-md-0 text-decoration-none" to={`/retailers/${ENV.SLUG}/orders/new`}>
                    + Crear orden
                  </Link>
                </div>
              </div>
            </div>

            <div className="col-md-12 d-flex align-items-center px-0">
              <div className="mr-12 search-input pl-0">
                <div className="input-group">
                  <span className="input-group-text">
                    <img src={ZoomIcon} alt="" />
                  </span>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Buscar por nombre del cliente"
                    value={searchText}
                    onChange={handleSearchText}
                  />
                </div>
              </div>
              <div className="mr-12">
                <button
                  type="button"
                  className="btn btn-outline-primary d-flex align-items-center"
                  data-bs-toggle="modal"
                  data-bs-target="#order_filters"
                >
                  <img src={FilterIcon} alt="" className="me-2" />
                  Filtros
                </button>
              </div>
            </div>

            <div className="col-md-12 px-0">
              <div className="row">
                <div className="col-md-12">
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <div className="btn-group" role="group">
                      <button
                        type="button"
                        className={`btn ${showSection === 'orders' ? 'btn-primary' : 'btn-outline-primary'}`}
                        onClick={() => handleShowSection('orders')}
                      >
                        Todas las órdenes
                      </button>
                      <button
                        type="button"
                        className={`btn ${showSection === 'pending_orders' ? 'btn-primary' : 'btn-outline-primary'}`}
                        onClick={() => handleShowSection('pending_orders')}
                      >
                        Pendientes ({totalPendindOrders})
                      </button>
                      <button
                        type="button"
                        className={`btn ${showSection === 'successful_orders' ? 'btn-primary' : 'btn-outline-primary'}`}
                        onClick={() => handleShowSection('successful_orders')}
                      >
                        Exitosas
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-md-12 px-0">
              <OrderTableList orders={orders} orderLoading={orderLoading} />
            </div>

            <div className="col-md-12 px-0">
              {total_pages > 1 && (
                <ReactPaginate
                  breakLabel="..."
                  nextLabel="Siguiente"
                  onPageChange={handlePageClick}
                  pageRangeDisplayed={5}
                  pageCount={total_pages}
                  forcePage={page - 1}
                  previousLabel="Anterior"
                  renderOnZeroPageCount={null}
                  containerClassName="pagination justify-content-center"
                  pageClassName="page"
                  activeClassName="a-black"
                  previousClassName="previous"
                  nextClassName="next"
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Orders;
