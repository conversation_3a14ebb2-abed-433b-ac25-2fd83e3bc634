require 'benchmark'

module Exports
  module Orders
    class RecordsJob < HybridJob

      def perform_job(filters, retailer_id, klass, customer_attrs, attributes, emails, format: 'xlsx')
        begin
          Rails.logger.info "🚀 [SUMMARY-JOB] Iniciando job para retailer #{retailer_id}"
          Rails.logger.info "🔍 [SUMMARY-JOB] Filtros recibidos: #{filters.inspect}"

          # Call the chain with the context
          Rails.logger.info "🔧 [SUMMARY-JOB] Llamando al Organizer..."
          result = Exports::Orders::Organizer.call({
                                            filters: filters,
                                            retailer_id: retailer_id,
                                            klass: klass,
                                            customer_attrs: customer_attrs,
                                            attributes: attributes,
                                            emails: emails,
                                            format: format,
                                            aws_s3_resource: Aws::S3::Resource.new
                                          })

          Rails.logger.info "📊 [SUMMARY-JOB] Organizer terminó. Success: #{result.success?}"
          Rails.logger.info "📊 [SUMMARY-JOB] Context keys: #{result.to_h.keys}"

          if result.success?
            # Marcar exportación como completada
            ExportStatus.complete_export(retailer_id, 'summary')
            Rails.logger.info "✅ [SUMMARY-JOB] Exportación completada exitosamente para retailer #{retailer_id}"
            Rails.logger.info "✅ [SUMMARY-JOB] Download history debería haberse creado"

            # Enviar webhook a Mercately
            download_url = result.download_history&.url || result.aws_obj&.public_url
            webhook_success = WebhookService.send_export_completed(
              retailer_id: retailer_id,
              export_type: 'summary',
              status: 'completed',
              download_url: download_url
            )

            if webhook_success
              Rails.logger.info "✅ [SUMMARY-JOB] Webhook enviado exitosamente a Mercately"
            else
              Rails.logger.warn "⚠️ [SUMMARY-JOB] Webhook falló, pero export completado. Usuario verá actualización via polling fallback"
            end
          else
            # Marcar exportación como fallida
            ExportStatus.fail_export(retailer_id, 'summary', result.message)
            Rails.logger.error "❌ [SUMMARY-JOB] Error en exportación para retailer #{retailer_id}: #{result.message}"

            # Enviar webhook de error a Mercately
            webhook_success = WebhookService.send_export_completed(
              retailer_id: retailer_id,
              export_type: 'summary',
              status: 'failed',
              error_message: result.message
            )

            if webhook_success
              Rails.logger.info "✅ [SUMMARY-JOB] Webhook de error enviado exitosamente a Mercately"
            else
              Rails.logger.warn "⚠️ [SUMMARY-JOB] Webhook de error falló, usuario verá actualización via polling fallback"
            end
          end

          result
        rescue => e
          # Marcar exportación como fallida
          ExportStatus.fail_export(retailer_id, 'summary', e.message)
          Rails.logger.error "❌ [SUMMARY-JOB] Excepción en exportación para retailer #{retailer_id}: #{e.message}"

          # Enviar webhook de error a Mercately
          webhook_success = WebhookService.send_export_completed(
            retailer_id: retailer_id,
            export_type: 'summary',
            status: 'failed',
            error_message: e.message
          )

          if webhook_success
            Rails.logger.info "✅ [SUMMARY-JOB] Webhook de excepción enviado exitosamente a Mercately"
          else
            Rails.logger.warn "⚠️ [SUMMARY-JOB] Webhook de excepción falló, usuario verá actualización via polling fallback"
          end

          raise e # Re-lanzar el error para que Sidekiq lo maneje
        end
      end
    end
  end
end
