# frozen_string_literal: true

# Clase base para trabajos que pueden funcionar tanto con Sidekiq como con DelayedJob
# 
# Esta clase permite que los jobs funcionen de manera transparente con ambos sistemas:
# - Con Sidekiq: usa perform_async y se ejecuta en background
# - Con DelayedJob: usa perform_later y se ejecuta sincrónicamente
#
# Uso:
#   class MyJob < HybridJob
#     # Opcional: configurar cola específica para Sidekiq
#     sidekiq_options queue: 'my_queue' if respond_to?(:sidekiq_options)
#     
#     def perform_job(*args, **kwargs)
#       # Tu lógica aquí
#     end
#   end
class HybridJob
  # Incluye Sidekiq::Worker y lo hace compatible con ActiveJob
  include Sidekiq::Worker if defined?(Sidekiq)

  # NOTA: Las subclases deben configurar su propia cola usando:
  # sidekiq_options queue: 'nombre_cola' if respond_to?(:sidekiq_options)

  # Para compatibilidad con DelayedJob, necesitamos que funcione como ActiveJob
  def self.perform_later(*args, **kwargs)
    # Crea una instancia y llama a perform
    job_instance = new
    if kwargs.any?
      job_instance.perform(**kwargs)
    else
      job_instance.perform(*args)
    end
  end

  # Para compatibilidad con Sidekiq, perform_async ya es proporcionado por Sidekiq::Worker

  # Maneja tanto argumentos posicionales como argumentos con palabra clave de Sidekiq
  def perform(*args, **kwargs)
    # Si recibimos un solo hash como primer argumento, lo tratamos como kwargs
    if args.size == 1 && args.first.is_a?(Hash) && kwargs.empty?
      hash_arg = args.first
      # Convierte las claves string a símbolos si es necesario
      normalized_kwargs = hash_arg.transform_keys(&:to_sym)
      perform_job(**normalized_kwargs)
    elsif kwargs.any?
      perform_job(**kwargs)
    else
      perform_job(*args)
    end
  end

  # Las subclases deben implementar este método
  def perform_job(*args, **kwargs)
    raise NotImplementedError, "Subclasses must implement #perform_job"
  end
end
