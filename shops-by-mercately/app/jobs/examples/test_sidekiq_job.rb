# frozen_string_literal: true

# Job de ejemplo para probar la infraestructura de Sidekiq
# 
# Este job puede ser usado por otros equipos para validar que la infraestructura
# funciona correctamente antes de migrar sus propios jobs.
#
# Uso:
#   QueueAdapterService.enqueue_job(
#     Examples::TestSidekiqJob,
#     retailer,
#     queue: :default,
#     message: "Test desde #{Time.current}",
#     retailer_id: retailer.id,
#     delay_seconds: 2
#   )
#
module Examples
  class TestSidekiqJob < HybridJob
    sidekiq_options queue: 'default' if respond_to?(:sidekiq_options)
    
    def perform_job(message:, retailer_id:, delay_seconds: 0)
      Rails.logger.info "🧪 [TEST-SIDEKIQ-JOB] Iniciando job para retailer #{retailer_id}"
      Rails.logger.info "🧪 [TEST-SIDEKIQ-JOB] Mensaje: #{message}"
      
      # Simular trabajo
      if delay_seconds > 0
        Rails.logger.info "🧪 [TEST-SIDEKIQ-JOB] Simulando trabajo por #{delay_seconds} segundos..."
        sleep(delay_seconds)
      end
      
      # Simular algún procesamiento
      result = process_test_data(retailer_id, message)
      
      Rails.logger.info "✅ [TEST-SIDEKIQ-JOB] Completado exitosamente para retailer #{retailer_id}"
      Rails.logger.info "✅ [TEST-SIDEKIQ-JOB] Resultado: #{result}"
      
      result
    end
    
    private
    
    def process_test_data(retailer_id, message)
      {
        status: 'success',
        retailer_id: retailer_id,
        message: message,
        processed_at: Time.current,
        worker_info: {
          hostname: Socket.gethostname,
          process_id: Process.pid,
          thread_id: Thread.current.object_id
        }
      }
    end
  end
end
