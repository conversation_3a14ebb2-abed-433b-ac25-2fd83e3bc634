# frozen_string_literal: true

class ExportStatus < ApplicationRecord
  belongs_to :retailer

  # Validaciones
  validates :export_type, presence: true, inclusion: { in: %w[summary detailed] }
  validates :status, presence: true, inclusion: { in: %w[in_progress completed failed] }
  validates :started_at, presence: true

  # Scopes
  scope :in_progress, -> { where(status: 'in_progress') }
  scope :completed, -> { where(status: 'completed') }
  scope :failed, -> { where(status: 'failed') }
  scope :for_retailer, ->(retailer_id) { where(retailer_id: retailer_id) }
  scope :by_type, ->(export_type) { where(export_type: export_type) }
  scope :recent, -> { order(started_at: :desc) }

  # Métodos de clase para verificar estados
  def self.retailer_has_export_in_progress?(retailer_id, export_type = nil)
    query = for_retailer(retailer_id).in_progress
    query = query.by_type(export_type) if export_type.present?
    query.exists?
  end

  def self.get_retailer_export_status(retailer_id)
    {
      summary: {
        in_progress: retailer_has_export_in_progress?(retailer_id, 'summary')
      },
      detailed: {
        in_progress: retailer_has_export_in_progress?(retailer_id, 'detailed')
      }
    }
  end

  # Métodos para marcar estados
  def self.start_export(retailer_id, export_type)
    # Limpiar exportaciones anteriores del mismo tipo que puedan haber quedado colgadas
    cleanup_old_exports(retailer_id, export_type)

    create!(
      retailer_id: retailer_id,
      export_type: export_type,
      status: 'in_progress',
      started_at: Time.current
    )
  end

  def self.complete_export(retailer_id, export_type)
    export_status = find_current_export(retailer_id, export_type)
    if export_status
      export_status.update!(
        status: 'completed',
        completed_at: Time.current
      )
    else
      Rails.logger.warn "⚠️ [EXPORT-STATUS] No se encontró exportación en progreso para retailer #{retailer_id}, tipo #{export_type}"
    end
  end

  def self.fail_export(retailer_id, export_type, error_message = nil)
    export_status = find_current_export(retailer_id, export_type)
    if export_status
      export_status.update!(
        status: 'failed',
        completed_at: Time.current,
        error_message: error_message
      )
    else
      Rails.logger.warn "⚠️ [EXPORT-STATUS] No se encontró exportación en progreso para retailer #{retailer_id}, tipo #{export_type}"
    end
  end

  # Métodos de instancia
  def in_progress?
    status == 'in_progress'
  end

  def completed?
    status == 'completed'
  end

  def failed?
    status == 'failed'
  end

  def duration
    return nil unless completed_at && started_at
    completed_at - started_at
  end

  private

  def self.find_current_export(retailer_id, export_type)
    for_retailer(retailer_id)
      .by_type(export_type)
      .in_progress
      .recent
      .first
  end

  def self.cleanup_old_exports(retailer_id, export_type)
    # Marcar como fallidas las exportaciones que llevan más de 2 horas en progreso
    old_exports = for_retailer(retailer_id)
                    .by_type(export_type)
                    .in_progress
                    .where('started_at < ?', 2.hours.ago)

    old_exports.update_all(
      status: 'failed',
      completed_at: Time.current,
      error_message: 'Exportación expirada por timeout'
    )

    if old_exports.count > 0
      Rails.logger.info "🧹 [EXPORT-STATUS] Limpiadas #{old_exports.count} exportaciones expiradas para retailer #{retailer_id}, tipo #{export_type}"
    end
  end
end
