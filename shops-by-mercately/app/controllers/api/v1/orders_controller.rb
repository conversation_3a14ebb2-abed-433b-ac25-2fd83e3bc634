# frozen_string_literal: true

class Api::V1::OrdersController < Api::V1::ApiController
  include SerializeConcern
  include Pagy::Backend

  before_action :find_order, only: %i[update show]

  def index
    filters = format_filters(query_orders_params)
    @orders = current_retailer.orders
      .where.not(archived: true)
      .order('id DESC')
      .ransack(filters)
      .result
    begin
      @pagy, @orders = pagy(@orders)
      total_pages = @pagy.pages
    rescue Pagy::OverflowError
      @orders = [].freeze
      total_pages = params[:page]
    end
    @orders = serialize(@orders, Api::V1::OrdersSerializer)
    render status: :ok, json: { orders: @orders, total_pages: total_pages }
  end

  def export
    # 🔍 Debug: Ver qué parámetros llegan
    Rails.logger.info "🔍 [EXPORT-DEBUG] params: #{params.inspect}"
    Rails.logger.info "🔍 [EXPORT-DEBUG] params[:q]: #{params[:q].inspect}"

    filters = format_filters(query_orders_params)
    Rails.logger.info "🔍 [EXPORT-DEBUG] filters after format: #{filters.inspect}"

    # Verificar si ya hay una exportación en progreso (sistema viejo)
    if current_retailer.has_pending_order_export?
      return render status: 409, json: {
        error: 'Ya hay una exportación en progreso. Por favor espere a que termine.'
      }
    end

    # Usar QueueAdapterService para encolado híbrido
    QueueAdapterService.enqueue_job(
      Exports::Orders::RecordsJob,
      current_retailer,
      filters,
      current_retailer.id,
      Order,
      Customer::PUBLIC_ATTRIBUTES,
      Order::PUBLIC_ATTRIBUTES,
      [agent_email_params[:email]],
      queue: :exports
    )

    Rails.logger.info "✅ [EXPORT] Job encolado exitosamente para retailer #{current_retailer.id}"

    render status: :ok, json: { message: 'En un momento recibirá un correo con un enlace para descargar el archivo' }
  end

  def detailed_export
    filters = format_filters(query_orders_params)

    # Verificar si ya hay una exportación detallada en progreso
    if ExportStatus.retailer_has_export_in_progress?(current_retailer.id, 'detailed')
      return render status: 409, json: {
        error: 'Ya hay una exportación detallada en progreso. Por favor espere a que termine.'
      }
    end

    # Iniciar tracking de la exportación
    ExportStatus.start_export(current_retailer.id, 'detailed')

    # Usar QueueAdapterService para encolado híbrido
    QueueAdapterService.enqueue_job(
      Exports::Orders::DetailedRecordsJob,
      current_retailer,
      queue: :exports,
      filters: filters,
      retailer_id: current_retailer.id,
      emails: [export_data_params[:email]]
    )

    Rails.logger.info "✅ [DETAILED-EXPORT] Job encolado exitosamente"

    # Devolver estado actualizado para que el frontend lo vea inmediatamente
    export_status = ExportStatus.get_retailer_export_status(current_retailer.id)

    render status: 200, json: {
      message: 'En un momento recibirá un correo con un enlace para descargar el archivo',
      export_status: export_status
    }
  end

  def export_status
    # Leer estado de exportaciones desde la base de datos
    begin
      Rails.logger.info "🔍 [EXPORT-STATUS] Consultando estado para retailer #{current_retailer.id}"

      # Verificar registros en la BD (forzar recarga para evitar cache)
      ExportStatus.connection.clear_query_cache
      in_progress_exports = ExportStatus.for_retailer(current_retailer.id).in_progress.reload
      Rails.logger.info "🔍 [EXPORT-STATUS] Exportaciones en progreso: #{in_progress_exports.count}"
      in_progress_exports.each do |export|
        Rails.logger.info "🔍 [EXPORT-STATUS] - #{export.export_type}: iniciada #{export.started_at}"
      end

      export_status = ExportStatus.get_retailer_export_status(current_retailer.id)
      Rails.logger.info "🔍 [EXPORT-STATUS] Estado final: #{export_status}"

      render status: 200, json: { export_status: export_status }
    rescue => e
      Rails.logger.error "❌ [EXPORT-STATUS] Error consultando estado: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      render status: 500, json: {
        error: 'Error consultando estado de exportación',
        export_status: {
          summary: { in_progress: false },
          detailed: { in_progress: false }
        }
      }
    end
  end

  def create
    begin
      order = current_retailer.orders.new(order_params.except(:customer_attributes))
      order.origin = :mercately
      order.is_new_address = order_params[:is_new_address]
      order.customer = find_or_create_customer unless order_params[:customer_id]
      if order.save
        render status: :ok, json: { order: serialize_order(order), message: "Orden creada con éxito" }
      else
        render status: :unprocessable_entity, json: { errors: order.errors.full_messages }
      end
    rescue StandardError => e
      Sentry.capture_exception(e)
      SlackError.send_error(e)
      render status: :unprocessable_entity, json: { errors: "Error al crear la orden" }
    end
  end

  def show
    render status: :ok, json: @order, serializer: Api::V1::OrderSerializer
  end

  def update
    begin
      customer = find_or_create_customer
      @order.is_new_address = order_params[:is_new_address]
      @order.customer = customer if customer
      if @order.update(order_params.except(:customer_attributes))
        render status: :ok, json: { order: serialize_order(@order), message: "Orden actualizada con éxito" }
      else
        render status: :unprocessable_entity, json: { errors: @order.errors.full_messages }
      end
    rescue StandardError => e
      puts"------------------------------#{e.inspect}"
      Sentry.capture_exception(e)
      SlackError.send_error(e)
      render status: :unprocessable_entity, json: { errors: "Error al actualizar la orden" }
    end
  end

  def update_by_id
    @order = current_retailer.orders.find(params[:id])
    customer = find_or_create_customer
    @order.customer = customer if customer
    if @order.update(order_params.except(:customer_attributes)) && upsert_custom_answers
      render status: :ok, json: @order
    else
      render status: :unprocessable_entity, json: {
        errors: @order.errors.full_messages
      }
    end
  end

  def pending
    render status: :ok, json: { pending_orders: current_retailer.orders.pending.count }
  end

  def last_quote
    quote = current_retailer.quotes.where.not(archived: true).last
    render status: :ok, json: { quote: quote.blank? ? nil : Api::V1::OrderSerializer.new(quote) }
  end

  def last_order
    order = current_retailer.orders.where.not(archived: true).last
    render status: :ok, json: { order: order.blank? ? nil : Api::V1::OrderSerializer.new(order) }
  end

  def download_histories
    @downloads = current_retailer.order_download_histories.order(id: :desc)
    @downloads = @downloads.ransack(params[:q]).result

    begin
      @pagy, @downloads = pagy(@downloads)
      total_pages = @pagy.pages
    rescue Pagy::OverflowError
      @downloads = [].freeze
      total_pages = params[:page]
    end
    @downloads = serialize(@downloads, Api::V1::OrderDownloadHistorySerializer)
    render status: 200, json: { downloads: @downloads, total_pages: total_pages }
  end

  private
    def find_order
      @order = current_retailer.orders.includes(:customer).find_by!(web_id: params[:id])
    end

    # Este método se creo porque en el create no tenemos el id de customer(que no se ha seleccionado el customer)
    # y hay varias metodos de encontrarlo

    def find_or_create_customer
      return if order_params[:customer_attributes].blank?

      if order_params[:customer_attributes][:id].present?
        customer = current_retailer.customers.find_by(id: order_params[:customer_attributes][:id])
      end

      if customer.blank? && order_params[:customer_attributes][:phone].present?
        customer = current_retailer.customers.find_by(phone: order_params[:customer_attributes][:phone])
        if customer.blank? && order_params[:customer_attributes][:country_id].present?
          phone = PhoneService.format_number(
            order_params[:customer_attributes][:phone],
            order_params[:customer_attributes][:country_id]
          )
          customer = current_retailer.customers.find_by(phone: phone)
        end
      end

      customer = current_retailer.customers.new(order_params[:customer_attributes]) if customer.blank?
      customer.is_new_address = order_params[:is_new_address]
      customer.order_params = order_params.except(:customer_attributes)

      if customer.id.blank?
        customer.save
      else
        begin
          customer.update(order_params[:customer_attributes])
        rescue => e
          Sentry.capture_exception(e)
          customer.save
        end
      end
      customer
    end

    def order_params
      params.require(:order).permit(
        :customer_id,
        :archived,
        :status,
        :sales_channel_id,
        :notes,
        :terms_and_conditions,
        :agent_name,
        :agent_id,
        :agent_email,
        :created_at,
        :delivery_method,
        :shipping_value,
        :shipping_cost_id,
        :address,
        :description,
        :city,
        :state,
        :zip_code,
        :country_id,
        :latitude,
        :longitude,
        :currency,
        :is_new_address,
        :discount_type,
        :discount_value,
        :discount_reason,
        payment_transactions_attributes: [
          :id,
          :retailer_id,
          :order_id,
          :customer_id,
          :transaction_id,
          :service,
          :amount,
          :status,
          :description,
          :status_detail,
          :note,
          :payment_method,
          :custom_payment_method,
          :_destroy,
          :attachment,
          :remove_attachment
        ],
        customer_attributes: [
          :id,
          :first_name,
          :last_name,
          :email,
          :phone,
          :id_type,
          :address,
          :city,
          :state,
          :country_id,
          :zip_code,
          :id_number,
          :web_id_in_mercately,
          :id_in_mercately
        ],
        order_items_attributes: [
          :id,
          :product_id,
          :product_variant_combination_id,
          :title,
          :variant_name,
          :quantity,
          :sold_wholesale,
          :unit_price,
          :custom_tax,
          :discount_type,
          :discount_value,
          :discount_reason,
          :_destroy,
          order_item_variants_attributes: [:id, :product_variant_option_id, :_destroy]
        ],
        custom_answers_attributes: [
          :id,
          :custom_field_id,
          :answer,
          :_destroy
        ],
        terms_and_condition_attributes: [
          :id,
          :terms_conditions_content
        ]
      )
    end

    def export_data_params
      params.require(:agent).permit(
        :email
      )
    end

    def serialize_order(order)
      ActiveModelSerializers::SerializableResource.new(
        order,
        serializer: Api::V1::OrderSerializer
      )
    end

    def format_filters(filters)
      return {} if filters.blank?
      return filters if filters[:created_at_gteq].blank? || filters[:created_at_lteq].blank?

      timezone = current_retailer.timezone.presence || 'America/Guayaquil'
      start_date = ActiveSupport::TimeZone[timezone].parse("#{filters[:created_at_gteq]} 00:00:00")
      end_date = ActiveSupport::TimeZone[timezone].parse("#{filters[:created_at_lteq]} 23:59:59")

      filters.merge(created_at_gteq: start_date, created_at_lteq: end_date)
    end

    def agent_email_params
      params.require(:agent).permit(:email)
    end

    def query_orders_params
      params.require(:q).permit(
        :customer_full_name_cont,
        :sequential_number_end,
        :agent_id_eq,
        :status_eq,
        :created_at_gteq,
        :created_at_lteq,
        :customer_web_id_in_mercately_eq
      )
    end
end
