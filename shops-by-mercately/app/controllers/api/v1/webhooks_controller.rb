# frozen_string_literal: true

class Api::V1::WebhooksController < ApplicationController
  # Saltear autenticación para webhooks (usaremos token específico)
  skip_before_action :verify_authenticity_token
  # skip_before_action :authenticate_user! # ← Comentado porque ApplicationController no lo define
  
  before_action :verify_webhook_token
  
  def export_completed
    begin
      Rails.logger.info "🔔 [WEBHOOK] Recibido webhook de export completado"
      Rails.logger.info "🔔 [WEBHOOK] Payload: #{webhook_params.inspect}"
      
      retailer_id = webhook_params[:retailer_id]
      export_type = webhook_params[:export_type] # 'summary' o 'detailed'
      status = webhook_params[:status] # 'completed', 'failed', etc.
      
      # Validar parámetros requeridos
      unless retailer_id && export_type && status
        Rails.logger.error "❌ [WEBHOOK] Parámetros faltantes: retailer_id=#{retailer_id}, export_type=#{export_type}, status=#{status}"
        return render json: { error: 'Parámetros requeridos faltantes' }, status: 400
      end
      
      # Construir el payload para el frontend
      export_status_payload = build_export_status_payload(export_type, status, webhook_params)
      
      # Enviar notificación al frontend via ActionCable
      ActionCable.server.broadcast(
        "exports_#{retailer_id}",
        {
          type: 'export_status_update',
          export_status: export_status_payload,
          timestamp: Time.current.iso8601
        }
      )
      
      Rails.logger.info "✅ [WEBHOOK] Notificación enviada via ActionCable a exports_#{retailer_id}"
      
      render json: { 
        message: 'Webhook procesado exitosamente',
        retailer_id: retailer_id,
        export_type: export_type,
        status: status
      }, status: 200
      
    rescue => e
      Rails.logger.error "❌ [WEBHOOK] Error procesando webhook: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      render json: { 
        error: 'Error interno procesando webhook',
        message: e.message 
      }, status: 500
    end
  end
  
  private
  
  def verify_webhook_token
    token = request.headers['X-Webhook-Token'] || params[:webhook_token]
    expected_token = ENV['SHOPS_WEBHOOK_TOKEN'] || 'mercately_webhook_secret_2024'
    
    unless token == expected_token
      Rails.logger.error "❌ [WEBHOOK] Token inválido: recibido=#{token}, esperado=#{expected_token}"
      render json: { error: 'Token de webhook inválido' }, status: 401
      return false
    end
    
    Rails.logger.info "✅ [WEBHOOK] Token verificado correctamente"
    true
  end
  
  def webhook_params
    params.permit(:retailer_id, :export_type, :status, :download_url, :error_message, :webhook_token)
  end
  
  def build_export_status_payload(export_type, status, params)
    base_payload = {
      exporting: false,
      summary: {},
      detailed: {}
    }
    
    case status
    when 'completed'
      base_payload[export_type.to_sym] = {
        in_progress: false,
        ready: true,
        download_url: params[:download_url],
        completed_at: Time.current.iso8601
      }
    when 'failed'
      base_payload[export_type.to_sym] = {
        in_progress: false,
        error: params[:error_message] || 'Error desconocido en la exportación',
        failed_at: Time.current.iso8601
      }
    else
      Rails.logger.warn "⚠️ [WEBHOOK] Status desconocido: #{status}"
      base_payload[export_type.to_sym] = {
        in_progress: false,
        status: status
      }
    end
    
    base_payload
  end
end
