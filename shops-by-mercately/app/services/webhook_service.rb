# frozen_string_literal: true

class WebhookService
  include HTTParty

  # Configuración base
  base_uri ENV['MERCATELY_BASE_URL'] || 'https://app.mercately.com'
  default_timeout 30
  
  def self.send_export_completed(retailer_id:, export_type:, status:, download_url: nil, error_message: nil)
    new.send_export_completed(
      retailer_id: retailer_id,
      export_type: export_type,
      status: status,
      download_url: download_url,
      error_message: error_message
    )
  end
  
  def send_export_completed(retailer_id:, export_type:, status:, download_url: nil, error_message: nil)
    endpoint = '/api/v1/webhooks/export_completed'
    
    payload = {
      retailer_id: retailer_id,
      export_type: export_type, # 'summary' o 'detailed'
      status: status, # 'completed', 'failed'
      download_url: download_url,
      error_message: error_message,
      timestamp: Time.current.iso8601,
      webhook_token: webhook_token
    }.compact
    
    Rails.logger.info "🔔 [WEBHOOK SENDER] Enviando webhook a Mercately"
    Rails.logger.info "🔔 [WEBHOOK SENDER] Endpoint: #{base_url}#{endpoint}"
    Rails.logger.info "🔔 [WEBHOOK SENDER] Payload: #{payload.except(:webhook_token).inspect}"
    
    begin
      response = self.class.post(
        endpoint,
        {
          body: payload.to_json,
          headers: {
            'Content-Type' => 'application/json',
            'X-Webhook-Token' => webhook_token,
            'User-Agent' => 'Shops-Webhook-Sender/1.0'
          }
        }
      )
      
      handle_response(response, payload)
      
    rescue Net::TimeoutError => e
      Rails.logger.error "⏰ [WEBHOOK SENDER] Timeout enviando webhook: #{e.message}"
      false
    rescue => e
      Rails.logger.error "❌ [WEBHOOK SENDER] Error enviando webhook: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end
  
  private
  
  def webhook_token
    @webhook_token ||= ENV['SHOPS_WEBHOOK_TOKEN'] || 'mercately_webhook_secret_2024'
  end
  
  def base_url
    self.class.base_uri
  end
  
  def handle_response(response, payload)
    case response.code
    when 200..299
      Rails.logger.info "✅ [WEBHOOK SENDER] Webhook enviado exitosamente"
      Rails.logger.info "✅ [WEBHOOK SENDER] Response: #{response.body}"
      true
    when 400..499
      Rails.logger.error "❌ [WEBHOOK SENDER] Error del cliente (#{response.code}): #{response.body}"
      Rails.logger.error "❌ [WEBHOOK SENDER] Payload enviado: #{payload.inspect}"
      false
    when 500..599
      Rails.logger.error "❌ [WEBHOOK SENDER] Error del servidor (#{response.code}): #{response.body}"
      false
    else
      Rails.logger.warn "⚠️ [WEBHOOK SENDER] Respuesta inesperada (#{response.code}): #{response.body}"
      false
    end
  rescue => e
    Rails.logger.error "❌ [WEBHOOK SENDER] Error procesando respuesta: #{e.message}"
    false
  end
end
