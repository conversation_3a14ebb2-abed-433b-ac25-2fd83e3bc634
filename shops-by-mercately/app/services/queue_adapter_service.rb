# frozen_string_literal: true

# Servicio para manejar el encolado de jobs tanto con Sidekiq como con DelayedJob
# 
# Este servicio permite migrar gradualmente de DelayedJob a Sidekiq usando feature flags
# basados en variables de entorno.
#
# Uso:
#   QueueAdapterService.enqueue_job(
#     MyJob,
#     retailer,
#     queue: :my_queue,
#     arg1: 'value1',
#     arg2: 'value2'
#   )
#
# Variables de entorno:
#   USE_SIDEKIQ_FOR_RETAILER=1,2,3  # IDs de retailers que usan Sidekiq
#
class QueueAdapterService
  DELAYED_JOB = 'delayed_job'
  SIDEKIQ = 'sidekiq'

  # Colas específicas de Sidekiq
  QUEUES = {
    exports: 'exports',
    imports: 'imports',
    woocommerce_sync: 'woocommerce_sync',
    shopify_sync: 'shopify_sync',
    mia_sync: 'mia_sync',
    default: 'default'
  }.freeze

  def self.adapter_for_retailer(retailer)
    # Verificar por variable de entorno
    if ENV['USE_SIDEKIQ_FOR_RETAILER']&.split(',')&.include?(retailer.id.to_s)
      SIDEKIQ
    else
      DELAYED_JOB
    end
  end

  def self.enqueue_job(job_class, retailer, *args, queue: nil, **kwargs)
    adapter = adapter_for_retailer(retailer)
    queue_name = queue || determine_queue_for_job(job_class)

    Rails.logger.info("🔄 [QUEUE-ADAPTER] Encolando #{job_class} para retailer #{retailer.id} usando #{adapter} en cola '#{queue_name}'")

    case adapter
    when SIDEKIQ
      # Para Sidekiq, no pasamos el retailer al job, solo los args
      enqueue_with_sidekiq(job_class, queue_name, *args, **kwargs)
    when DELAYED_JOB
      # Para DelayedJob, pasamos el retailer como primer argumento
      enqueue_with_delayed_job(job_class, retailer, *args, **kwargs)
    else
      raise ArgumentError, "Adaptador de cola desconocido: #{adapter}"
    end
  end

  # Determinar cola automáticamente basado en el nombre del job
  def self.determine_queue_for_job(job_class)
    job_name = job_class.name.downcase

    case job_name
    when /export/
      QUEUES[:exports]
    when /import/
      QUEUES[:imports]
    when /woocommerce/
      QUEUES[:woocommerce_sync]
    when /shopify/
      QUEUES[:shopify_sync]
    when /mia/
      QUEUES[:mia_sync]
    else
      QUEUES[:default]
    end
  end

  def self.enqueue_with_sidekiq(job_class, queue_name, *args, **kwargs)
    # Usar perform_async directo para todos los jobs (Sidekiq::Worker puro)
    begin
      if kwargs.any?
        # Jobs con keyword arguments
        job_class.perform_async(**kwargs)
      else
        # Jobs con argumentos posicionales
        job_class.perform_async(*args)
      end

      Rails.logger.info("✅ [SIDEKIQ] Job encolado exitosamente: #{job_class}")
    rescue => e
      Rails.logger.error("❌ [SIDEKIQ] Error al encolar job: #{e.message}")
      raise e
    end
  end

  def self.enqueue_with_delayed_job(job_class, *args, **kwargs)
    # Para HybridJob, usar perform_later que ejecuta sincrónicamente
    begin
      job_class.perform_later(*args, **kwargs)
      Rails.logger.info("✅ [DELAYED-JOB] Job ejecutado sincrónicamente: #{job_class}")
    rescue => e
      Rails.logger.error("❌ [DELAYED-JOB] Error ejecutando job: #{e.message}")
      raise e
    end
  end

  # Método para verificar el estado de las colas
  def self.queue_stats
    stats = {}

    # Stats de Delayed Job
    if defined?(Delayed::Job)
      stats[:delayed_job] = {
        pending: Delayed::Job.where(failed_at: nil, locked_at: nil).count,
        working: Delayed::Job.where(failed_at: nil).where.not(locked_at: nil).count,
        failed: Delayed::Job.where.not(failed_at: nil).count
      }
    end

    # Stats de Sidekiq
    if defined?(Sidekiq)
      sidekiq_stats = Sidekiq::Stats.new
      stats[:sidekiq] = {
        pending: sidekiq_stats.enqueued,
        working: sidekiq_stats.processed,
        failed: sidekiq_stats.failed
      }
    end

    stats
  end

  # Método para obtener retailers que usan Sidekiq
  def self.sidekiq_retailers
    # Si hay un campo en la base de datos
    if Retailer.column_names.include?('use_sidekiq')
      Retailer.where(use_sidekiq: true)
    else
      # Usar variable de entorno como fallback
      retailer_ids = ENV['USE_SIDEKIQ_FOR_RETAILER']&.split(',') || []
      Retailer.where(id: retailer_ids)
    end
  end
end
