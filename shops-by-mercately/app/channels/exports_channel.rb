# frozen_string_literal: true

class ExportsChannel < ApplicationCable::Channel
  def subscribed
    # Obtener retailer_id del parámetro
    retailer_id = params[:retailer_id]
    
    if retailer_id.present?
      stream_name = "exports_#{retailer_id}"
      stream_from stream_name
      
      Rails.logger.info "📡 [EXPORTS CHANNEL] Cliente suscrito a #{stream_name}"
      
      # Enviar confirmación de suscripción
      transmit({
        type: 'subscription_confirmed',
        message: 'Suscrito a notificaciones de export',
        retailer_id: retailer_id,
        timestamp: Time.current.iso8601
      })
    else
      Rails.logger.error "❌ [EXPORTS CHANNEL] retailer_id faltante en suscripción"
      reject
    end
  end

  def unsubscribed
    retailer_id = params[:retailer_id]
    Rails.logger.info "📡 [EXPORTS CHANNEL] Cliente desuscrito de exports_#{retailer_id}"
  end

  def ping(data)
    # Método para verificar conectividad
    retailer_id = params[:retailer_id]
    Rails.logger.info "🏓 [EXPORTS CHANNEL] Ping recibido de retailer #{retailer_id}"
    
    transmit({
      type: 'pong',
      message: 'Canal activo',
      timestamp: Time.current.iso8601
    })
  end
end
