# ✅ Checklist de Deployment - Sistema Híbrido de Exports

## 🚀 Pre-Deployment

### **Verificaciones de Código**
- [ ] **Tests pasando** en ambos repos (Shops + Mercately)
- [ ] **Linting sin errores** en JavaScript
- [ ] **Rubocop sin errores** en Ruby
- [ ] **Commits bien documentados** con mensajes claros

### **Configuración de Variables**
- [ ] **MERCATELY_BASE_URL** configurada en Shops
- [ ] **SHOPS_WEBHOOK_TOKEN** configurada en ambos sistemas
- [ ] **ActionCable configurado** en Mercately (Redis si multi-server)
- [ ] **RETAILER_ID disponible** en ENV del frontend

### **Dependencias**
- [ ] **HTTParty gem** disponible en Shops
- [ ] **ActionCable** habilitado en Mercately
- [ ] **Redis** configurado si hay múltiples servidores

## 🔧 Deployment Steps

### **1. Deploy Mercately (Receptor)**
```bash
# 1. Deploy backend (receptor de webhooks)
git checkout feature/MER-2114-improve-orders-export-ux
bundle install
rails db:migrate  # Si hay migraciones pendientes
rails assets:precompile

# 2. Verificar endpoint
curl -X POST https://app.mercately.com/api/v1/webhooks/export_completed \
  -H "X-Webhook-Token: test" \
  -d '{"test": true}'
# Debe responder 401 (token inválido) - significa que endpoint funciona

# 3. Verificar ActionCable
# En browser console después del deploy:
# exportsChannelManager.subscribe(123, {})
# Debe conectar sin errores
```

### **2. Deploy Shops (Sender)**
```bash
# 1. Deploy backend (sender de webhooks)
git checkout temp/MER-2114-clean
bundle install
rails db:migrate  # Para ExportStatus si no está

# 2. Verificar WebhookService
rails console
WebhookService.send_export_completed(
  retailer_id: 123,
  export_type: 'test',
  status: 'completed'
)
# Debe enviar request a Mercately
```

### **3. Verificación Post-Deploy**
- [ ] **Webhook endpoint** responde correctamente
- [ ] **ActionCable** conecta en browser
- [ ] **Logs aparecen** en ambos sistemas
- [ ] **Frontend carga** sin errores de JavaScript

## 🧪 Testing en Producción

### **Test 1: Flujo Normal (Webhook)**
1. **Iniciar export** desde frontend
2. **Verificar logs** en Shops: `grep "WEBHOOK SENDER" log/production.log`
3. **Verificar logs** en Mercately: `grep "WEBHOOK.*Recibido" log/production.log`
4. **Verificar UI** se actualiza instantáneamente
5. **Tiempo esperado:** <5 segundos total

### **Test 2: Flujo de Fallback (Polling)**
1. **Temporalmente deshabilitar** webhooks (cambiar URL)
2. **Iniciar export** desde frontend
3. **Esperar 30 segundos** para timeout
4. **Verificar logs** de polling: `grep "FALLBACK POLLING" log/production.log`
5. **Verificar UI** se actualiza via polling
6. **Restaurar** configuración de webhooks

### **Test 3: Manejo de Errores**
1. **Forzar error** en export (datos inválidos)
2. **Verificar webhook** de error enviado
3. **Verificar UI** muestra mensaje de error
4. **Verificar logs** completos del flujo

## 📊 Monitoreo Post-Deploy

### **Métricas a Vigilar (Primeras 24h)**

**Shops:**
```bash
# Webhooks enviados exitosamente
grep -c "WEBHOOK SENDER.*exitosamente" log/production.log

# Webhooks fallidos
grep -c "WEBHOOK SENDER.*Error" log/production.log

# Exports completados
grep -c "EXPORT.*completada exitosamente" log/production.log
```

**Mercately:**
```bash
# Webhooks recibidos
grep -c "WEBHOOK.*Recibido webhook" log/production.log

# ActionCable conexiones
grep -c "EXPORTS CHANNEL.*suscrito" log/production.log

# Polling fallback activado
grep -c "FALLBACK POLLING.*Activando" log/production.log
```

### **Alertas Críticas**
- [ ] **>50% webhooks fallando** → Investigar conectividad
- [ ] **>30% polling fallback** → Problema con webhooks
- [ ] **Exports >10 minutos** → Problema de performance
- [ ] **ActionCable desconexiones** → Problema de WebSocket

## 🚨 Rollback Plan

### **Si hay problemas críticos:**

**Opción 1: Rollback Completo**
```bash
# Mercately
git checkout previous-stable-branch
bundle install && rails assets:precompile

# Shops  
git checkout previous-stable-branch
bundle install
```

**Opción 2: Deshabilitar Webhooks (Fallback a Polling)**
```bash
# En Shops, comentar líneas de webhook en jobs:
# WebhookService.send_export_completed(...)

# Sistema funcionará 100% con polling como antes
```

**Opción 3: Fix Forward**
```bash
# Para problemas menores, fix y redeploy
# Logs detallados facilitan debugging rápido
```

## 🔍 Debugging en Producción

### **Comandos Útiles**

**Ver logs en tiempo real:**
```bash
# Shops
tail -f log/production.log | grep -E "WEBHOOK|EXPORT.*JOB"

# Mercately
tail -f log/production.log | grep -E "WEBHOOK|ACTIONCABLE"
```

**Verificar estado de exports:**
```bash
# En Shops console
ExportStatus.in_progress.count
ExportStatus.for_retailer(123).recent.limit(10)
```

**Verificar ActionCable:**
```bash
# En Mercately console
ActionCable.server.connections.count
```

### **Problemas Comunes y Soluciones**

**Webhook timeout:**
- Verificar `MERCATELY_BASE_URL`
- Verificar conectividad de red
- Verificar logs de firewall

**ActionCable no conecta:**
- Verificar configuración de WebSocket
- Verificar Redis si multi-server
- Verificar browser console errors

**Polling no se activa:**
- Verificar lógica de `shouldUsePollingFallback()`
- Verificar estados de React
- Verificar logs de frontend

## ✅ Sign-off

### **Antes de marcar como completado:**
- [ ] **Tests manuales** completados exitosamente
- [ ] **Métricas baseline** establecidas
- [ ] **Alertas configuradas** en monitoring
- [ ] **Equipo notificado** del nuevo sistema
- [ ] **Documentación** compartida con el equipo
- [ ] **Rollback plan** validado y listo

### **Responsables:**
- **Backend (Shops):** _______________
- **Backend (Mercately):** _______________  
- **Frontend:** _______________
- **DevOps/Monitoring:** _______________
- **QA/Testing:** _______________

---

**Deployment para:** MER-2114 Sistema Híbrido de Exports  
**Fecha objetivo:** _______________  
**Estado:** [ ] Ready [ ] In Progress [ ] Completed [ ] Rolled Back
