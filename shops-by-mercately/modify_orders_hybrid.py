#!/usr/bin/env python3
"""
Script para modificar el componente Orders y convertirlo a sistema híbrido
"""

import re

def modify_orders_component():
    file_path = 'app/javascript/components/Orders/index.js'
    
    # Leer el archivo
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔍 Archivo leído exitosamente")
    
    # 1. Reemplazar el bloque del polling antiguo
    old_polling_pattern = r'  // Redux-based polling para export status\s*useEffect\(\(\) => \{.*?\}, \[dispatch\]\);'
    
    new_polling_code = '''  // Función para determinar si debe usar polling fallback
  const shouldUsePollingFallback = () => {
    const isExportingNow = exportStatus.exporting || 
                          exportStatus.summary?.in_progress || 
                          exportStatus.detailed?.in_progress;
    
    if (!isExportingNow) return false;
    
    // Activar polling si:
    // 1. Webhook timeout (no llegó webhook en 30 segundos)
    // 2. Export lleva más de 2 minutos
    // 3. Usuario reconectó con export activo
    const exportTakingTooLong = exportStartTime && 
                               (Date.now() - exportStartTime) > 120000; // 2 minutos
    
    return webhookTimeout || exportTakingTooLong || !exportStartTime;
  };

  // Polling condicional (solo como fallback)
  useEffect(() => {
    let interval = null;
    
    if (shouldUsePollingFallback()) {
      console.log('🔄 [FALLBACK POLLING] Activando polling fallback');
      setPollingActive(true);
      
      const checkExportStatus = () => {
        console.log('🔄 [FALLBACK POLLING] Verificando estado...');
        dispatch(fetchExportStatus());
      };

      checkExportStatus(); // Llamada inicial
      interval = setInterval(checkExportStatus, 5000); // Cada 5 segundos (más frecuente)
    } else {
      console.log('🚫 [FALLBACK POLLING] Polling desactivado - esperando webhooks');
      setPollingActive(false);
    }

    return () => {
      if (interval) {
        console.log('🔄 [FALLBACK POLLING] Limpiando polling...');
        clearInterval(interval);
      }
    };
  }, [dispatch, webhookTimeout, exportStartTime, exportStatus]);

  // Timeout para webhook - activar polling si no llega webhook
  useEffect(() => {
    let timeoutId = null;
    
    const isExportingNow = exportStatus.exporting || 
                          exportStatus.summary?.in_progress || 
                          exportStatus.detailed?.in_progress;
    
    if (isExportingNow && !webhookTimeout && exportStartTime) {
      console.log('⏰ [WEBHOOK TIMEOUT] Configurando timeout de 30 segundos...');
      
      timeoutId = setTimeout(() => {
        console.log('⏰ [WEBHOOK TIMEOUT] Webhook no llegó - activando polling fallback');
        setWebhookTimeout(true);
      }, 30000); // 30 segundos
    }
    
    // Si export terminó, resetear timeout
    if (!isExportingNow && webhookTimeout) {
      console.log('✅ [WEBHOOK TIMEOUT] Export terminado - reseteando timeout');
      setWebhookTimeout(false);
      setExportStartTime(null);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [exportStatus, webhookTimeout, exportStartTime]);'''
    
    # Hacer el reemplazo con DOTALL para que . incluya newlines
    content = re.sub(old_polling_pattern, new_polling_code, content, flags=re.DOTALL)
    print("✅ Polling híbrido reemplazado")
    
    # 2. Modificar las funciones de export para registrar tiempo de inicio
    # Export normal
    old_export = r'(const exportFilteredOrder = \(\) => \{[^}]*if \(confirm\([^)]*\)\) \{[^}]*console\.log\([^)]*\);)\s*(dispatch\(startOrderExport\(search\)\);)'
    new_export = r'\1\n      setExportStartTime(Date.now()); // Registrar tiempo de inicio\n      setWebhookTimeout(false); // Reset timeout\n      \2'
    content = re.sub(old_export, new_export, content, flags=re.DOTALL)
    print("✅ Función exportFilteredOrder modificada")
    
    # Export detallado
    old_detailed_export = r'(const exportFilteredDetailedOrder = \(\) => \{[^}]*if \(confirm\([^)]*\)\) \{[^}]*console\.log\([^)]*\);)\s*(dispatch\(startDetailedOrderExport\(search\)\);)'
    new_detailed_export = r'\1\n      setExportStartTime(Date.now()); // Registrar tiempo de inicio\n      setWebhookTimeout(false); // Reset timeout\n      \2'
    content = re.sub(old_detailed_export, new_detailed_export, content, flags=re.DOTALL)
    print("✅ Función exportFilteredDetailedOrder modificada")
    
    # 3. Modificar el texto del botón para mostrar estado de polling
    old_button_text = r'const getExportButtonText = \(\) => \{\s*if \(isExporting\(\)\) return "Exportando\.\.\.";\s*if \(hasReadyDownload\(\)\) return "Descargar";\s*return "Exportar";\s*\};'
    new_button_text = '''const getExportButtonText = () => {
    if (isExporting()) {
      return pollingActive ? "Exportando... (verificando)" : "Exportando...";
    }
    if (hasReadyDownload()) return "Descargar";
    return "Exportar";
  };'''
    content = re.sub(old_button_text, new_button_text, content, flags=re.DOTALL)
    print("✅ Texto del botón modificado")
    
    # 4. Agregar logs adicionales al useEffect de exportStatus
    old_log = r'// Log cuando cambie el exportStatus\s*useEffect\(\(\) => \{\s*console\.log\([^)]*exportStatus\);\s*\}, \[exportStatus\]\);'
    new_log = '''// Log cuando cambie el exportStatus
  useEffect(() => {
    console.log('📊 [REDUX STATE] Export status updated:', exportStatus);
    console.log('🔄 [POLLING STATE] Polling active:', pollingActive);
    console.log('⏰ [WEBHOOK STATE] Webhook timeout:', webhookTimeout);
  }, [exportStatus, pollingActive, webhookTimeout]);'''
    content = re.sub(old_log, new_log, content, flags=re.DOTALL)
    print("✅ Logs adicionales agregados")
    
    # Escribir el archivo modificado
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("🎉 ¡Archivo modificado exitosamente!")
    print("📋 Cambios realizados:")
    print("   - ✅ Polling convertido a sistema híbrido")
    print("   - ✅ Funciones de export registran tiempo de inicio")
    print("   - ✅ Botón muestra estado de polling")
    print("   - ✅ Logs mejorados para debugging")

if __name__ == "__main__":
    modify_orders_component()
