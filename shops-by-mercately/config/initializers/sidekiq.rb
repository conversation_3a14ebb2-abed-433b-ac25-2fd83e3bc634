# frozen_string_literal: true

# Configuración de Sidekiq para shops_by_mercately
# Este inicializador configura Sidekiq como alternativa a delayed_job

Sidekiq.configure_server do |config|
  # Configuración de Redis para Sidekiq
  redis_config = if ENV['ENVIRONMENT'] == 'production'
                   { url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/2') }
                 else
                   { url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/2') }
                 end

  config.redis = redis_config

  # Configuración de logging
  if Rails.env.production?
    sidekiq_logger = Logger.new("#{Rails.root}/log/sidekiq.log")
    config.logger = sidekiq_logger
    config.logger.level = ENV.fetch('RAILS_LOG_LEVEL', 'info').to_sym
  end

  # Configuración de concurrencia
  config.concurrency = ENV.fetch('SIDEKIQ_CONCURRENCY', 5).to_i

  Rails.logger.info("🚀 [SIDEKIQ] Servidor configurado con Redis: #{redis_config[:url]}")
end

Sidekiq.configure_client do |config|
  # Misma configuración de Redis para el cliente
  redis_config = if ENV['ENVIRONMENT'] == 'production'
                   { url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/2') }
                 else
                   { url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/2') }
                 end

  config.redis = redis_config

  Rails.logger.info("📡 [SIDEKIQ] Cliente configurado con Redis: #{redis_config[:url]}")
end

# Configuración de colas por defecto
Sidekiq.default_job_options = {
  'backtrace' => true,
  'retry' => 3
}

# Desactivar validación estricta de argumentos para permitir símbolos y objetos complejos
# Esto permite que Sidekiq maneje automáticamente la serialización de tipos no-JSON nativos
Sidekiq.strict_args!(false)

Rails.logger.info("⚙️ [SIDEKIQ] Inicializador cargado correctamente con strict_args = false")
