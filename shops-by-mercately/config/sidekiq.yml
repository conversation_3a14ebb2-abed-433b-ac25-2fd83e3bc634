# Sidekiq configuration for shops-by-mercately
# Configuración de colas específicas por tipo de trabajo

:concurrency: 5

# Colas organizadas por prioridad y tipo de trabajo
:queues:
  - exports           # Exportaciones de órdenes (alta prioridad)
  - imports           # Importaciones de productos (alta prioridad)
  - woocommerce_sync  # Sincronización WooCommerce
  - shopify_sync      # Sincronización Shopify
  - mia_sync          # Sincronización MIA
  - default           # Jobs generales/fallback

# Configuración de Redis
:redis:
  url: redis://localhost:6379/2

# Configuración de retry
:retry: 3

# Configuración de timeout
:timeout: 30

# Configuración de logging
:logfile: ./log/sidekiq.log
:pidfile: ./tmp/pids/sidekiq.pid

# Configuración para diferentes entornos
:production:
  :concurrency: 10
  :timeout: 60

:staging:
  :concurrency: 5
  :timeout: 30

:development:
  :concurrency: 2
  :timeout: 15
