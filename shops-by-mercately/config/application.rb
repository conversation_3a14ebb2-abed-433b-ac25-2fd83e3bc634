require_relative "boot"

require "rails/all"
require 'aws-sdk-s3'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module MercatelyShop
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")

    # Don't generate system test files.
    config.generators.system_tests = nil

    config.active_job.queue_adapter = :delayed_job

    config.middleware.insert_before 0, Rack::Cors do
      allow do
        case ENV['ENVIRONMENT']
        when 'production'
          origins 'mercately.com', 'www.mercately.com', 'app.mercately.com'
        when 'staging'
          origins 'staging.mercately.com', 'staging-pruebas.mercately.com'
        else
          origins '*'
        end
        # resource '/api/v1/*', headers: :any, methods: %i[get post put patch delete options]
      end
    end

    Aws.config.update(
      region: ENV['AWS_REGION'],
      access_key_id: ENV['AWS_ACCESS_KEY_ID'],
      secret_access_key: ENV['AWS_SECRET_ACCESS_KEY']
    )

    config.autoload_paths += %W[#{config.root}/app/interactors/shared]
    config.autoload_paths.push(
      Rails.root.join('lib'),
    )
  end
end
