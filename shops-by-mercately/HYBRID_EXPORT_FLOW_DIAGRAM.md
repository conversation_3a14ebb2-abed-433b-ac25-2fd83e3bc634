# 🔄 Diagrama de Flujo - Sistema Híbrido de Exports

## 📊 <PERSON><PERSON><PERSON> (Webhooks)

```mermaid
sequenceDiagram
    participant U as Usuario
    participant F as Frontend (React)
    participant M as Mercately API
    participant S as Shops API
    participant J as Job (Sidekiq)
    participant W as WebhookService
    participant A as ActionCable

    U->>F: Click "Exportar"
    F->>F: setExportStartTime(now)
    F->>F: setWebhookTimeout(false)
    F->>A: Subscribe to exports_channel
    F->>M: POST /orders/detailed_export
    M->>S: POST /api/v1/retailers/orders/detailed_export
    S->>J: Enqueue DetailedRecordsJob
    S->>M: Response: "Job encolado"
    M->>F: Response: "En proceso"
    F->>F: Show "Exportando..."
    
    Note over F: Timeout de 30s configurado
    
    J->>J: Procesar exportación
    J->>J: ExportStatus.complete_export()
    J->>W: send_export_completed()
    W->>M: POST /webhooks/export_completed
    M->>M: Validate webhook token
    M->>A: Broadcast to exports_123
    A->>F: export_status_update
    F->>F: updateExportStatusFromWebhook()
    F->>F: Redux updated
    F->>U: Show "Descargar" button
```

## ⏰ Flujo de Fallback (Polling)

```mermaid
sequenceDiagram
    participant F as Frontend
    participant T as Timer (30s)
    participant P as Polling
    participant M as Mercately API

    F->>T: Start webhook timeout
    Note over T: 30 segundos...
    T->>F: Timeout triggered
    F->>F: setWebhookTimeout(true)
    F->>P: shouldUsePollingFallback() = true
    P->>P: setPollingActive(true)
    
    loop Every 5 seconds
        P->>M: GET /orders/export_status
        M->>P: Current export status
        P->>F: Update Redux if changed
        Note over P: Continue until export complete
    end
    
    P->>F: Export completed detected
    P->>P: setPollingActive(false)
    F->>F: Show "Descargar" button
```

## 🔀 Diagrama de Estados

```mermaid
stateDiagram-v2
    [*] --> Idle: Initial state
    
    Idle --> Exporting: User clicks export
    
    state Exporting {
        [*] --> WaitingWebhook: Export started
        WaitingWebhook --> WebhookReceived: Webhook arrives < 30s
        WaitingWebhook --> PollingFallback: Timeout 30s
        PollingFallback --> PollingActive: Start polling
        PollingActive --> PollingActive: Check every 5s
        PollingActive --> Completed: Status changed
        WebhookReceived --> Completed: Instant update
    }
    
    Exporting --> Completed: Export finished
    Exporting --> Failed: Export error
    Completed --> Idle: User downloads/dismisses
    Failed --> Idle: User acknowledges error
```

## 🏗️ Arquitectura de Componentes

```mermaid
graph TB
    subgraph "Frontend (Mercately)"
        UI[Orders Component]
        Redux[Redux Store]
        ACM[ActionCable Manager]
        Polling[Polling Logic]
    end
    
    subgraph "Backend (Mercately)"
        WC[WebhooksController]
        EC[ExportsChannel]
        AC[ActionCable Server]
    end
    
    subgraph "Backend (Shops)"
        API[Orders API]
        Job[Export Jobs]
        WS[WebhookService]
        ES[ExportStatus Model]
    end
    
    UI --> Redux
    UI --> ACM
    UI --> Polling
    ACM --> EC
    EC --> AC
    AC --> Redux
    Polling --> API
    
    Job --> ES
    Job --> WS
    WS --> WC
    WC --> AC
    
    style UI fill:#e1f5fe
    style Redux fill:#f3e5f5
    style Job fill:#fff3e0
    style WS fill:#e8f5e8
```

## 🔄 Flujo de Decisión

```mermaid
flowchart TD
    Start([Usuario inicia export]) --> SetTimer[Configurar timeout 30s]
    SetTimer --> Subscribe[Suscribirse a ActionCable]
    Subscribe --> SendRequest[Enviar request a Shops]
    SendRequest --> WaitWebhook{Esperar webhook}
    
    WaitWebhook -->|Webhook llega < 30s| WebhookReceived[Webhook recibido]
    WaitWebhook -->|Timeout 30s| ActivatePolling[Activar polling]
    
    WebhookReceived --> UpdateUI[Actualizar UI instantáneamente]
    
    ActivatePolling --> CheckConditions{Verificar condiciones}
    CheckConditions -->|Export activo| StartPolling[Iniciar polling cada 5s]
    CheckConditions -->|Export terminado| StopPolling[Detener polling]
    
    StartPolling --> PollAPI[Consultar API]
    PollAPI --> StatusChanged{Estado cambió?}
    StatusChanged -->|Sí| UpdateUI
    StatusChanged -->|No| Wait5s[Esperar 5s]
    Wait5s --> PollAPI
    
    UpdateUI --> Complete([Export completado])
    StopPolling --> Complete
    
    style Start fill:#c8e6c9
    style Complete fill:#ffcdd2
    style WebhookReceived fill:#e1f5fe
    style ActivatePolling fill:#fff3e0
```

## 📱 Estados de UI

```mermaid
stateDiagram-v2
    [*] --> ButtonEnabled: "Exportar"
    
    ButtonEnabled --> ButtonDisabled: User clicks
    
    state ButtonDisabled {
        [*] --> Exporting: "Exportando..."
        Exporting --> ExportingVerifying: Polling active
        ExportingVerifying --> Exporting: Webhook received
        Exporting --> Ready: Export completed
        Exporting --> Error: Export failed
    }
    
    ButtonDisabled --> ButtonEnabled: Export finished
    
    note right of Exporting
        Spinner icon
        Webhook expected
    end note
    
    note right of ExportingVerifying
        Spinner icon
        "(verificando)" text
        Polling active
    end note
    
    note right of Ready
        Download icon
        "Descargar" text
    end note
```

---

**Diagramas generados para:** MER-2114 Sistema Híbrido de Exports  
**Herramienta:** Mermaid.js  
**Fecha:** Agosto 2024
