// Script para modificar el componente Orders y agregar sistema híbrido
const fs = require('fs');

const filePath = 'app/javascript/components/Orders/index.js';
const content = fs.readFileSync(filePath, 'utf8');

// Buscar y reemplazar el useEffect del polling
const oldPollingCode = `  // Redux-based polling para export status
  useEffect(() => {
    console.log('🔍 [REDUX POLLING] Starting Redux-based polling...');

    const checkExportStatus = () => {
      console.log('🔍 [REDUX POLLING] Dispatching fetchExportStatus...');
      dispatch(fetchExportStatus());
    };

    checkExportStatus(); // Llamada inicial
    const interval = setInterval(checkExportStatus, 10000); // Cada 10 segundos

    return () => {
      console.log('🔍 [REDUX POLLING] Cleaning up polling...');
      clearInterval(interval);
    };
  }, [dispatch]);`;

const newHybridCode = `  // Estados para el sistema híbrido
  const [webhookTimeout, setWebhookTimeout] = useState(false);
  const [exportStartTime, setExportStartTime] = useState(null);
  const [pollingActive, setPollingActive] = useState(false);

  // Función para determinar si debe usar polling fallback
  const shouldUsePollingFallback = () => {
    const isExportingNow = exportStatus.exporting || 
                          exportStatus.summary?.in_progress || 
                          exportStatus.detailed?.in_progress;
    
    if (!isExportingNow) return false;
    
    // Activar polling si:
    // 1. Webhook timeout (no llegó webhook en 30 segundos)
    // 2. Export lleva más de 2 minutos
    // 3. Usuario reconectó con export activo
    const exportTakingTooLong = exportStartTime && 
                               (Date.now() - exportStartTime) > 120000; // 2 minutos
    
    return webhookTimeout || exportTakingTooLong || !exportStartTime;
  };

  // Polling condicional (solo como fallback)
  useEffect(() => {
    let interval = null;
    
    if (shouldUsePollingFallback()) {
      console.log('🔄 [FALLBACK POLLING] Activando polling fallback');
      setPollingActive(true);
      
      const checkExportStatus = () => {
        console.log('🔄 [FALLBACK POLLING] Verificando estado...');
        dispatch(fetchExportStatus());
      };

      checkExportStatus(); // Llamada inicial
      interval = setInterval(checkExportStatus, 5000); // Cada 5 segundos (más frecuente)
    } else {
      console.log('🚫 [FALLBACK POLLING] Polling desactivado - esperando webhooks');
      setPollingActive(false);
    }

    return () => {
      if (interval) {
        console.log('🔄 [FALLBACK POLLING] Limpiando polling...');
        clearInterval(interval);
      }
    };
  }, [dispatch, webhookTimeout, exportStartTime, exportStatus]);

  // Timeout para webhook - activar polling si no llega webhook
  useEffect(() => {
    let timeoutId = null;
    
    const isExportingNow = exportStatus.exporting || 
                          exportStatus.summary?.in_progress || 
                          exportStatus.detailed?.in_progress;
    
    if (isExportingNow && !webhookTimeout && exportStartTime) {
      console.log('⏰ [WEBHOOK TIMEOUT] Configurando timeout de 30 segundos...');
      
      timeoutId = setTimeout(() => {
        console.log('⏰ [WEBHOOK TIMEOUT] Webhook no llegó - activando polling fallback');
        setWebhookTimeout(true);
      }, 30000); // 30 segundos
    }
    
    // Si export terminó, resetear timeout
    if (!isExportingNow && webhookTimeout) {
      console.log('✅ [WEBHOOK TIMEOUT] Export terminado - reseteando timeout');
      setWebhookTimeout(false);
      setExportStartTime(null);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [exportStatus, webhookTimeout, exportStartTime]);`;

// Reemplazar el código
let newContent = content.replace(oldPollingCode, newHybridCode);

// Modificar las funciones de export para registrar tiempo de inicio
const oldExportFunction = `  const exportFilteredOrder = () => {
    if (isExportDisabled()) return; // Prevenir clicks durante export

    if (confirm('¿Estás seguro de exportar órdenes con los filtros seleccionados?')) {
      console.log('🚀 [EXPORT] Starting summary export...');
      dispatch(startOrderExport(search));
    }
  };`;

const newExportFunction = `  const exportFilteredOrder = () => {
    if (isExportDisabled()) return; // Prevenir clicks durante export

    if (confirm('¿Estás seguro de exportar órdenes con los filtros seleccionados?')) {
      console.log('🚀 [EXPORT] Starting summary export...');
      setExportStartTime(Date.now()); // Registrar tiempo de inicio
      setWebhookTimeout(false); // Reset timeout
      dispatch(startOrderExport(search));
    }
  };`;

newContent = newContent.replace(oldExportFunction, newExportFunction);

const oldDetailedExportFunction = `  const exportFilteredDetailedOrder = () => {
    if (isExportDisabled()) return; // Prevenir clicks durante export

    if (confirm('¿Estás seguro de exportar detalle de órdenes con los filtros seleccionados?')) {
      console.log('🚀 [EXPORT] Starting detailed export...');
      dispatch(startDetailedOrderExport(search));
    }
  };`;

const newDetailedExportFunction = `  const exportFilteredDetailedOrder = () => {
    if (isExportDisabled()) return; // Prevenir clicks durante export

    if (confirm('¿Estás seguro de exportar detalle de órdenes con los filtros seleccionados?')) {
      console.log('🚀 [EXPORT] Starting detailed export...');
      setExportStartTime(Date.now()); // Registrar tiempo de inicio
      setWebhookTimeout(false); // Reset timeout
      dispatch(startDetailedOrderExport(search));
    }
  };`;

newContent = newContent.replace(oldDetailedExportFunction, newDetailedExportFunction);

// Modificar el texto del botón para mostrar estado de polling
const oldButtonText = `  const getExportButtonText = () => {
    if (isExporting()) return "Exportando...";
    if (hasReadyDownload()) return "Descargar";
    return "Exportar";
  };`;

const newButtonText = `  const getExportButtonText = () => {
    if (isExporting()) {
      return pollingActive ? "Exportando... (verificando)" : "Exportando...";
    }
    if (hasReadyDownload()) return "Descargar";
    return "Exportar";
  };`;

newContent = newContent.replace(oldButtonText, newButtonText);

// Agregar logs adicionales
const oldLogUseEffect = `  // Log cuando cambie el exportStatus
  useEffect(() => {
    console.log('📊 [REDUX STATE] Export status updated:', exportStatus);
  }, [exportStatus]);`;

const newLogUseEffect = `  // Log cuando cambie el exportStatus
  useEffect(() => {
    console.log('📊 [REDUX STATE] Export status updated:', exportStatus);
    console.log('🔄 [POLLING STATE] Polling active:', pollingActive);
    console.log('⏰ [WEBHOOK STATE] Webhook timeout:', webhookTimeout);
  }, [exportStatus, pollingActive, webhookTimeout]);`;

newContent = newContent.replace(oldLogUseEffect, newLogUseEffect);

// Escribir el archivo modificado
fs.writeFileSync(filePath, newContent);

console.log('✅ Archivo modificado exitosamente!');
