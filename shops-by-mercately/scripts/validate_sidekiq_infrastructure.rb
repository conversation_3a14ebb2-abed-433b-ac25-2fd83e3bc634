#!/usr/bin/env ruby
# frozen_string_literal: true

# Script para validar que la infraestructura de Sidekiq funciona correctamente
# 
# Uso:
#   rails runner scripts/validate_sidekiq_infrastructure.rb
#   
# O con retailer específico:
#   RETAILER_ID=123 rails runner scripts/validate_sidekiq_infrastructure.rb

class SidekiqInfrastructureValidator
  def self.run
    puts "🔍 Validando infraestructura de Sidekiq..."
    puts "=" * 50
    
    validator = new
    validator.validate_all
    
    puts "=" * 50
    puts validator.success? ? "✅ Validación completada exitosamente" : "❌ Validación falló"
  end
  
  def initialize
    @errors = []
    @warnings = []
    @retailer = find_test_retailer
  end
  
  def validate_all
    validate_configuration
    validate_redis_connection
    validate_hybrid_job
    validate_queue_adapter_service
    validate_web_interface
    validate_job_enqueuing
    
    print_summary
  end
  
  def success?
    @errors.empty?
  end
  
  private
  
  def find_test_retailer
    retailer_id = ENV['RETAILER_ID']
    if retailer_id
      Retailer.find(retailer_id)
    else
      Retailer.first || create_test_retailer
    end
  end
  
  def create_test_retailer
    puts "⚠️  No se encontró retailer, creando uno de prueba..."
    Retailer.create!(
      name: 'Test Retailer for Sidekiq',
      email: '<EMAIL>',
      phone: '1234567890'
    )
  end
  
  def validate_configuration
    puts "\n📋 Validando configuración..."
    
    # Verificar archivos de configuración
    config_files = [
      'config/sidekiq.yml',
      'config/initializers/sidekiq.rb'
    ]
    
    config_files.each do |file|
      if File.exist?(file)
        puts "  ✅ #{file} existe"
      else
        @errors << "❌ #{file} no existe"
      end
    end
    
    # Verificar que Sidekiq esté en el Gemfile
    if File.read('Gemfile').include?('sidekiq')
      puts "  ✅ Sidekiq está en el Gemfile"
    else
      @errors << "❌ Sidekiq no está en el Gemfile"
    end
    
    # Verificar que las clases estén disponibles
    begin
      HybridJob
      puts "  ✅ HybridJob está disponible"
    rescue NameError
      @errors << "❌ HybridJob no está disponible"
    end
    
    begin
      QueueAdapterService
      puts "  ✅ QueueAdapterService está disponible"
    rescue NameError
      @errors << "❌ QueueAdapterService no está disponible"
    end
  end
  
  def validate_redis_connection
    puts "\n🔗 Validando conexión a Redis..."
    
    begin
      if defined?(Sidekiq)
        Sidekiq.redis { |conn| conn.ping }
        puts "  ✅ Conexión a Redis exitosa"
      else
        @warnings << "⚠️  Sidekiq no está cargado, no se puede validar Redis"
      end
    rescue => e
      @errors << "❌ Error conectando a Redis: #{e.message}"
    end
  end
  
  def validate_hybrid_job
    puts "\n🔧 Validando HybridJob..."
    
    begin
      # Crear job de prueba
      test_job = Examples::TestSidekiqJob.new
      
      # Probar perform_job directamente
      result = test_job.perform_job(
        message: 'Validation test',
        retailer_id: @retailer.id,
        delay_seconds: 0
      )
      
      if result[:status] == 'success'
        puts "  ✅ HybridJob.perform_job funciona correctamente"
      else
        @errors << "❌ HybridJob.perform_job no retornó éxito"
      end
      
    rescue => e
      @errors << "❌ Error en HybridJob: #{e.message}"
    end
  end
  
  def validate_queue_adapter_service
    puts "\n⚙️  Validando QueueAdapterService..."
    
    begin
      # Probar detección de adapter
      adapter = QueueAdapterService.adapter_for_retailer(@retailer)
      puts "  ✅ Adapter detectado: #{adapter}"
      
      # Probar detección de cola
      queue = QueueAdapterService.determine_queue_for_job(Examples::TestSidekiqJob)
      puts "  ✅ Cola detectada: #{queue}"
      
      # Probar estadísticas
      stats = QueueAdapterService.queue_stats
      puts "  ✅ Estadísticas obtenidas: #{stats.keys.join(', ')}"
      
    rescue => e
      @errors << "❌ Error en QueueAdapterService: #{e.message}"
    end
  end
  
  def validate_web_interface
    puts "\n🌐 Validando interfaz web..."
    
    begin
      if defined?(Sidekiq::Web)
        puts "  ✅ Sidekiq::Web está disponible"
        puts "  ℹ️  Accesible en: /sidekiq"
      else
        @warnings << "⚠️  Sidekiq::Web no está disponible"
      end
    rescue => e
      @warnings << "⚠️  Error validando interfaz web: #{e.message}"
    end
  end
  
  def validate_job_enqueuing
    puts "\n📤 Validando encolado de jobs..."

    begin
      original_env = ENV['USE_SIDEKIQ_FOR_RETAILER']

      # Probar con DelayedJob (sin retailer en la lista)
      puts "  🔍 Probando con DelayedJob (retailer NO en lista)..."
      ENV.delete('USE_SIDEKIQ_FOR_RETAILER')

      adapter = QueueAdapterService.adapter_for_retailer(@retailer)
      if adapter == 'delayed_job'
        puts "    ✅ DelayedJob adapter funciona"
      else
        @warnings << "⚠️  DelayedJob adapter no se activó correctamente"
      end

      # Probar con Sidekiq (agregando retailer a la lista)
      puts "  🔍 Probando con Sidekiq (retailer EN lista)..."
      ENV['USE_SIDEKIQ_FOR_RETAILER'] = @retailer.id.to_s

      adapter = QueueAdapterService.adapter_for_retailer(@retailer)
      if adapter == 'sidekiq'
        puts "    ✅ Sidekiq adapter funciona"
      else
        @warnings << "⚠️  Sidekiq adapter no se activó correctamente"
      end

      # Restaurar configuración original ANTES del test de encolado
      if original_env
        ENV['USE_SIDEKIQ_FOR_RETAILER'] = original_env
      else
        ENV.delete('USE_SIDEKIQ_FOR_RETAILER')
      end

      # Probar encolado real con la configuración ORIGINAL
      puts "  🧪 Probando encolado real con configuración original..."
      current_adapter = QueueAdapterService.adapter_for_retailer(@retailer)
      puts "    ℹ️  Adapter actual para retailer #{@retailer.id}: #{current_adapter}"

      # Temporalmente deshabilitar la ejecución real
      original_delay_jobs = nil
      if defined?(Delayed::Worker)
        original_delay_jobs = Delayed::Worker.delay_jobs
        Delayed::Worker.delay_jobs = false
      end

      # Intentar encolar un job de prueba
      begin
        QueueAdapterService.enqueue_job(
          Examples::TestSidekiqJob,
          @retailer,
          queue: :default,
          message: 'Validation test',
          retailer_id: @retailer.id,
          delay_seconds: 0
        )
        puts "    ✅ Job se encoló exitosamente usando #{current_adapter}"
      rescue => enqueue_error
        @warnings << "⚠️  Error encolando job: #{enqueue_error.message}"
      end

      # Restaurar configuración original
      if defined?(Delayed::Worker) && !original_delay_jobs.nil?
        Delayed::Worker.delay_jobs = original_delay_jobs
      end

    rescue => e
      @errors << "❌ Error validando encolado: #{e.message}"
    end
  end
  
  def print_summary
    puts "\n📊 Resumen de validación:"
    
    if @errors.any?
      puts "\n❌ Errores encontrados:"
      @errors.each { |error| puts "  #{error}" }
    end
    
    if @warnings.any?
      puts "\n⚠️  Advertencias:"
      @warnings.each { |warning| puts "  #{warning}" }
    end
    
    if @errors.empty? && @warnings.empty?
      puts "  🎉 ¡Todo perfecto! La infraestructura está lista para usar."
    end
  end
end

# Ejecutar validación si el script se llama directamente
if __FILE__ == $0
  SidekiqInfrastructureValidator.run
end
