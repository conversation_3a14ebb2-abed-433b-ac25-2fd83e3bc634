require 'rails_helper'

RSpec.describe HybridJob, type: :job do
  # Create a test job class that inherits from HybridJob
  let(:test_job_class) do
    Class.new(HybridJob) do
      def perform_job(*args, **kwargs)
        # Store the arguments for testing
        @performed_args = args
        @performed_kwargs = kwargs
        "job_executed"
      end

      attr_reader :performed_args, :performed_kwargs
    end
  end

  let(:test_job) { test_job_class.new }

  describe 'class methods' do
    describe '.perform_later' do
      context 'with positional arguments' do
        it 'creates instance and calls perform with args' do
          expect_any_instance_of(test_job_class).to receive(:perform).with('arg1', 'arg2')
          test_job_class.perform_later('arg1', 'arg2')
        end
      end

      context 'with keyword arguments' do
        it 'creates instance and calls perform with kwargs' do
          expect_any_instance_of(test_job_class).to receive(:perform).with(key1: 'value1', key2: 'value2')
          test_job_class.perform_later(key1: 'value1', key2: 'value2')
        end
      end
    end
  end

  describe 'instance methods' do
    describe '#perform' do
      context 'with positional arguments' do
        it 'calls perform_job with the same arguments' do
          test_job.perform('arg1', 'arg2')
          expect(test_job.performed_args).to eq(['arg1', 'arg2'])
          expect(test_job.performed_kwargs).to eq({})
        end
      end

      context 'with keyword arguments' do
        it 'calls perform_job with the same keyword arguments' do
          test_job.perform(key1: 'value1', key2: 'value2')
          expect(test_job.performed_args).to eq([])
          expect(test_job.performed_kwargs).to eq({ key1: 'value1', key2: 'value2' })
        end
      end

      context 'with single hash argument (Sidekiq serialization)' do
        it 'converts hash argument to keyword arguments' do
          test_job.perform({ 'key1' => 'value1', 'key2' => 'value2' })
          expect(test_job.performed_args).to eq([])
          expect(test_job.performed_kwargs).to eq({ key1: 'value1', key2: 'value2' })
        end

        it 'handles symbol keys in hash' do
          test_job.perform({ key1: 'value1', key2: 'value2' })
          expect(test_job.performed_args).to eq([])
          expect(test_job.performed_kwargs).to eq({ key1: 'value1', key2: 'value2' })
        end
      end

      context 'with mixed arguments' do
        it 'prioritizes keyword arguments over positional when both present' do
          test_job.perform('arg1', key1: 'value1')
          expect(test_job.performed_args).to eq([])
          expect(test_job.performed_kwargs).to eq({ key1: 'value1' })
        end
      end
    end

    describe '#perform_job' do
      let(:base_job) { HybridJob.new }

      it 'raises NotImplementedError when not overridden' do
        expect { base_job.perform_job }.to raise_error(NotImplementedError, "Subclasses must implement #perform_job")
      end
    end
  end

  describe 'Sidekiq integration' do
    context 'when Sidekiq is defined' do
      before do
        # Ensure Sidekiq is defined for this test
        stub_const('Sidekiq', Module.new) unless defined?(Sidekiq)
        stub_const('Sidekiq::Worker', Module.new) unless defined?(Sidekiq::Worker)

        # Mock sidekiq_options method
        allow(test_job_class).to receive(:respond_to?).and_return(true)
        allow(test_job_class).to receive(:sidekiq_options)
      end

      it 'includes Sidekiq::Worker' do
        # In newer versions of Sidekiq, it includes Sidekiq::Job instead of Sidekiq::Worker
        ancestors = test_job_class.ancestors.map(&:to_s)
        expect(ancestors).to include('Sidekiq::Worker').or include('Sidekiq::Job')
      end

      it 'configures sidekiq_options with exports queue' do
        expect(test_job_class).to receive(:sidekiq_options).with(queue: 'exports')
        test_job_class.class_eval { sidekiq_options queue: 'exports' if respond_to?(:sidekiq_options) }
      end
    end
  end

  describe 'DelayedJob compatibility' do
    it 'provides perform_later method for DelayedJob compatibility' do
      expect(test_job_class).to respond_to(:perform_later)
    end

    it 'can be instantiated and called directly' do
      result = test_job.perform('test_arg')
      expect(result).to eq("job_executed")
    end
  end

  describe 'argument handling edge cases' do
    context 'with empty arguments' do
      it 'handles no arguments' do
        test_job.perform
        expect(test_job.performed_args).to eq([])
        expect(test_job.performed_kwargs).to eq({})
      end
    end

    context 'with nil arguments' do
      it 'handles nil as argument' do
        test_job.perform(nil)
        expect(test_job.performed_args).to eq([nil])
        expect(test_job.performed_kwargs).to eq({})
      end
    end

    context 'with multiple hash arguments' do
      it 'treats multiple hashes as positional arguments' do
        test_job.perform({ key1: 'value1' }, { key2: 'value2' })
        expect(test_job.performed_args).to eq([{ key1: 'value1' }, { key2: 'value2' }])
        expect(test_job.performed_kwargs).to eq({})
      end
    end
  end
end
