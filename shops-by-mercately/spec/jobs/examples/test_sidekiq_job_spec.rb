require 'rails_helper'

RSpec.describe Examples::TestSidekiqJob, type: :job do
  let(:retailer) { create(:retailer) }
  let(:job) { described_class.new }
  
  describe '#perform_job' do
    let(:message) { 'Test message from RSpec' }
    
    context 'with basic parameters' do
      it 'processes the job successfully' do
        result = job.perform_job(
          message: message,
          retailer_id: retailer.id
        )
        
        expect(result).to be_a(Hash)
        expect(result[:status]).to eq('success')
        expect(result[:retailer_id]).to eq(retailer.id)
        expect(result[:message]).to eq(message)
        expect(result[:processed_at]).to be_a(Time)
        expect(result[:worker_info]).to include(:hostname, :process_id, :thread_id)
      end
    end
    
    context 'with delay parameter' do
      it 'simulates work delay' do
        start_time = Time.current
        
        job.perform_job(
          message: message,
          retailer_id: retailer.id,
          delay_seconds: 1
        )
        
        end_time = Time.current
        expect(end_time - start_time).to be >= 1
      end
    end
    
    context 'logging' do
      it 'logs the expected messages' do
        expect(Rails.logger).to receive(:info).with(/🧪 \[TEST-SIDEKIQ-JOB\] Iniciando job/)
        expect(Rails.logger).to receive(:info).with(/🧪 \[TEST-SIDEKIQ-JOB\] Mensaje:/)
        expect(Rails.logger).to receive(:info).with(/✅ \[TEST-SIDEKIQ-JOB\] Completado exitosamente/)
        expect(Rails.logger).to receive(:info).with(/✅ \[TEST-SIDEKIQ-JOB\] Resultado:/)
        
        job.perform_job(
          message: message,
          retailer_id: retailer.id
        )
      end
    end
  end
  
  describe 'integration with QueueAdapterService' do
    context 'when using Sidekiq' do
      before do
        ENV['USE_SIDEKIQ_FOR_RETAILER'] = retailer.id.to_s
        allow(described_class).to receive(:perform_async)
      end
      
      after do
        ENV.delete('USE_SIDEKIQ_FOR_RETAILER')
      end
      
      it 'enqueues the job with Sidekiq' do
        expect(described_class).to receive(:perform_async).with(
          message: 'Test message',
          retailer_id: retailer.id,
          delay_seconds: 2
        )
        
        QueueAdapterService.enqueue_job(
          described_class,
          retailer,
          queue: :default,
          message: 'Test message',
          retailer_id: retailer.id,
          delay_seconds: 2
        )
      end
    end
    
    context 'when using DelayedJob' do
      before do
        ENV.delete('USE_SIDEKIQ_FOR_RETAILER')
        allow(described_class).to receive(:perform_later)
      end
      
      it 'enqueues the job with DelayedJob' do
        expect(described_class).to receive(:perform_later).with(
          retailer,
          message: 'Test message',
          retailer_id: retailer.id,
          delay_seconds: 2
        )
        
        QueueAdapterService.enqueue_job(
          described_class,
          retailer,
          queue: :default,
          message: 'Test message',
          retailer_id: retailer.id,
          delay_seconds: 2
        )
      end
    end
  end
end
