require 'rails_helper'

RSpec.describe Mercately::OrderService, type: :service do
  let(:retailer) { create(:retailer) }
  let(:customer) { create(:customer, retailer:) }
  let(:order) { create(:order, retailer:, customer:) }
  let(:service) { described_class.new(order) }

  describe '#initialize' do
    it 'sets the order and retailer' do
      expect(service.instance_variable_get(:@order)).to eq(order)
      expect(service.instance_variable_get(:@retailer)).to eq(retailer)
    end
  end

  describe '#send_notification' do
    let(:response) { instance_double(HTTParty::Response, code: 200, parsed_response: { 'success' => true }) }

    before do
      allow(HTTParty).to receive(:post).and_return(response)
    end

    it 'posts to the correct URL with headers and body' do
      expect(HTTParty).to receive(:post).with(
        "#{service.instance_variable_get(:@mercately_url)}/api/v1/shops/retailers/send_order_notification",
        body: anything,
        headers: anything
      )
      service.send_notification
    end

    context 'when response code is not 200' do
      let(:response) { instance_double(HTTParty::Response, code: 500, parsed_response: { 'error' => 'failed' }) }

      it 'raises an error' do
        expect { service.send_notification }.to raise_error(RuntimeError, /Mercately Response/)
      end
    end
  end

  describe '#save_order_data' do
    let(:response) { instance_double(HTTParty::Response, code: 200, parsed_response: { 'success' => true }) }

    before do
      allow(HTTParty).to receive(:post).and_return(response)
    end

    it 'posts to save_order_data endpoint' do
      expect(HTTParty).to receive(:post).with(
        "#{service.instance_variable_get(:@mercately_url)}/api/v1/shops/retailers/save_order_data",
        body: anything,
        headers: anything
      )
      service.save_order_data
    end
  end

  describe 'private methods' do
    describe '#get_answer' do
      it 'returns Si for true values' do
        answer = OpenStruct.new(answer: 'true')
        expect(service.send(:get_answer, answer)).to eq('Si')
      end

      it 'returns No for false values' do
        answer = OpenStruct.new(answer: 'f')
        expect(service.send(:get_answer, answer)).to eq('No')
      end

      it 'returns original answer otherwise' do
        answer = OpenStruct.new(answer: 'Other')
        expect(service.send(:get_answer, answer)).to eq('Other')
      end
    end

    describe '#get_list_answer' do
      let(:options) { [{ 'value' => '1', 'label' => 'Option 1' }] }

      it 'returns the label for matching value' do
        expect(service.send(:get_list_answer, '1', options)).to eq('Option 1')
      end

      it 'returns nil if no match' do
        expect(service.send(:get_list_answer, '2', options)).to be_nil
      end
    end
  end
end
