require 'rails_helper'

RSpec.describe QueueAdapterService, type: :service do
  let(:retailer) { create(:retailer) }
  let(:test_job_class) do
    Class.new(HybridJob) do
      def perform_job(*args, **kwargs)
        "job_executed"
      end
    end
  end

  before do
    # Mock Sidekiq methods
    allow(test_job_class).to receive(:perform_async)
    allow(test_job_class).to receive(:perform_later)
  end

  describe '.adapter_for_retailer' do
    context 'when retailer is in USE_SIDEKIQ_FOR_RETAILER env var' do
      before do
        ENV['USE_SIDEKIQ_FOR_RETAILER'] = "#{retailer.id},999"
      end

      after do
        ENV.delete('USE_SIDEKIQ_FOR_RETAILER')
      end

      it 'returns sidekiq' do
        expect(QueueAdapterService.adapter_for_retailer(retailer)).to eq('sidekiq')
      end
    end

    context 'when retailer is not in USE_SIDEKIQ_FOR_RETAILER env var' do
      before do
        ENV['USE_SIDEKIQ_FOR_RETAILER'] = '999,888'
      end

      after do
        ENV.delete('USE_SIDEKIQ_FOR_RETAILER')
      end

      it 'returns delayed_job' do
        expect(QueueAdapterService.adapter_for_retailer(retailer)).to eq('delayed_job')
      end
    end

    context 'when USE_SIDEKIQ_FOR_RETAILER env var is not set' do
      before do
        ENV.delete('USE_SIDEKIQ_FOR_RETAILER')
      end

      it 'returns delayed_job' do
        expect(QueueAdapterService.adapter_for_retailer(retailer)).to eq('delayed_job')
      end
    end
  end

  describe '.determine_queue_for_job' do
    it 'returns exports queue for export jobs' do
      export_job = double('ExportJob', name: 'Exports::Orders::DetailedRecordsJob')
      expect(QueueAdapterService.determine_queue_for_job(export_job)).to eq('exports')
    end

    it 'returns imports queue for import jobs' do
      import_job = double('ImportJob', name: 'Imports::Products::CsvImportJob')
      expect(QueueAdapterService.determine_queue_for_job(import_job)).to eq('imports')
    end

    it 'returns woocommerce_sync queue for woocommerce jobs' do
      woo_job = double('WooJob', name: 'Integrations::WooCommerce::SyncJob')
      expect(QueueAdapterService.determine_queue_for_job(woo_job)).to eq('woocommerce_sync')
    end

    it 'returns default queue for unknown jobs' do
      unknown_job = double('UnknownJob', name: 'SomeRandomJob')
      expect(QueueAdapterService.determine_queue_for_job(unknown_job)).to eq('default')
    end
  end

  describe '.enqueue_job' do
    context 'when using Sidekiq' do
      before do
        ENV['USE_SIDEKIQ_FOR_RETAILER'] = retailer.id.to_s
        allow(QueueAdapterService).to receive(:enqueue_with_sidekiq)
      end

      after do
        ENV.delete('USE_SIDEKIQ_FOR_RETAILER')
      end

      it 'calls enqueue_with_sidekiq' do
        expect(QueueAdapterService).to receive(:enqueue_with_sidekiq)
          .with(test_job_class, 'default', arg1: 'value1')

        QueueAdapterService.enqueue_job(test_job_class, retailer, arg1: 'value1')
      end
    end

    context 'when using DelayedJob' do
      before do
        ENV.delete('USE_SIDEKIQ_FOR_RETAILER')
        allow(QueueAdapterService).to receive(:enqueue_with_delayed_job)
      end

      it 'calls enqueue_with_delayed_job' do
        expect(QueueAdapterService).to receive(:enqueue_with_delayed_job)
          .with(test_job_class, retailer, arg1: 'value1')

        QueueAdapterService.enqueue_job(test_job_class, retailer, arg1: 'value1')
      end
    end
  end

  describe '.enqueue_with_sidekiq' do
    context 'with keyword arguments' do
      it 'calls perform_async with kwargs' do
        expect(test_job_class).to receive(:perform_async).with(arg1: 'value1', arg2: 'value2')

        QueueAdapterService.enqueue_with_sidekiq(test_job_class, 'default', arg1: 'value1', arg2: 'value2')
      end
    end

    context 'with positional arguments' do
      it 'calls perform_async with args' do
        expect(test_job_class).to receive(:perform_async).with('arg1', 'arg2')

        QueueAdapterService.enqueue_with_sidekiq(test_job_class, 'default', 'arg1', 'arg2')
      end
    end
  end

  describe '.enqueue_with_delayed_job' do
    it 'calls perform_later' do
      expect(test_job_class).to receive(:perform_later).with(retailer, arg1: 'value1')

      QueueAdapterService.enqueue_with_delayed_job(test_job_class, retailer, arg1: 'value1')
    end
  end
end
