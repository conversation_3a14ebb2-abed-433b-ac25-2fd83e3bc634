GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.0.1)
      actionpack (= 7.0.1)
      activesupport (= 7.0.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (7.0.1)
      actionpack (= 7.0.1)
      activejob (= 7.0.1)
      activerecord (= 7.0.1)
      activestorage (= 7.0.1)
      activesupport (= 7.0.1)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.0.1)
      actionpack (= 7.0.1)
      actionview (= 7.0.1)
      activejob (= 7.0.1)
      activesupport (= 7.0.1)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (7.0.1)
      actionview (= 7.0.1)
      activesupport (= 7.0.1)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (7.0.1)
      actionpack (= 7.0.1)
      activerecord (= 7.0.1)
      activestorage (= 7.0.1)
      activesupport (= 7.0.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.0.1)
      activesupport (= 7.0.1)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_model_serializers (0.10.13)
      actionpack (>= 4.1, < 7.1)
      activemodel (>= 4.1, < 7.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    active_storage_validations (0.9.6)
      activejob (>= 5.2.0)
      activemodel (>= 5.2.0)
      activestorage (>= 5.2.0)
      activesupport (>= 5.2.0)
    activejob (7.0.1)
      activesupport (= 7.0.1)
      globalid (>= 0.3.6)
    activemodel (7.0.1)
      activesupport (= 7.0.1)
    activemodel-serializers-xml (1.0.3)
      activemodel (>= 5.0.0.a)
      activesupport (>= 5.0.0.a)
      builder (~> 3.1)
    activerecord (7.0.1)
      activemodel (= 7.0.1)
      activesupport (= 7.0.1)
    activeresource (6.1.4)
      activemodel (>= 6.0)
      activemodel-serializers-xml (~> 1.0)
      activesupport (>= 6.0)
    activestorage (7.0.1)
      actionpack (= 7.0.1)
      activejob (= 7.0.1)
      activerecord (= 7.0.1)
      activesupport (= 7.0.1)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (7.0.1)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.0)
      public_suffix (>= 2.0.2, < 5.0)
    airbrussh (1.4.0)
      sshkit (>= 1.6.1, != 1.7.0)
    aws-eventstream (1.2.0)
    aws-partitions (1.550.0)
    aws-sdk-core (3.125.5)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.525.0)
      aws-sigv4 (~> 1.1)
      jmespath (~> 1.0)
    aws-sdk-kms (1.53.0)
      aws-sdk-core (~> 3, >= 3.125.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.111.3)
      aws-sdk-core (~> 3, >= 3.125.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sigv4 (1.4.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bigdecimal (3.1.8)
    bindex (0.8.1)
    bootsnap (1.10.2)
      msgpack (~> 1.2)
    builder (3.2.4)
    byebug (11.1.3)
    capistrano (3.16.0)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.0.1)
      capistrano (~> 3.1)
    capistrano-rails (1.6.1)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rails-console (2.3.0)
      capistrano (>= 3.5.0, < 4.0.0)
      sshkit-interactive (~> 0.3.0)
    capistrano-rails-logs-tail (1.0.5)
      capistrano (>= 3.4.0, < 4.0.0)
      capistrano-rails
    capistrano-rvm (0.1.2)
      capistrano (~> 3.0)
      sshkit (~> 1.2)
    capistrano-systemd-multiservice (0.1.0.beta12)
      capistrano (>= 3.7.0, < 3.17.0)
    capistrano-yarn (2.0.2)
      capistrano (~> 3.0)
    carrierwave (2.2.6)
      activemodel (>= 5.0.0)
      activesupport (>= 5.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      mini_mime (>= 0.1.3)
      ssrf_filter (~> 1.0)
    carrierwave_backgrounder (1.0.2)
      carrierwave (> 2.0, < 4.0)
      rails (> 6.0, < 8.0)
    case_transform (0.2)
      activesupport
    caxlsx (3.4.1)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.3)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    concurrent-ruby (1.1.10)
    connection_pool (2.5.3)
    countries (5.0.0)
      i18n_data (~> 0.16.0)
      sixarm_ruby_unaccent (~> 1.1)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.11.0)
      addressable
    currency_select (4.0.0)
      actionview (>= 5.2.0, < 7.1)
      money (~> 6.0)
    daemons (1.4.1)
    delayed_job (4.1.10)
      activesupport (>= 3.0, < 8.0)
    delayed_job_active_record (4.1.7)
      activerecord (>= 3.0, < 8.0)
      delayed_job (>= 3.0, < 5)
    diff-lcs (1.5.0)
    digest (3.1.0)
    docile (1.4.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.7.6)
    dotenv-rails (2.7.6)
      dotenv (= 2.7.6)
      railties (>= 3.2)
    dox (2.1.0)
      activesupport (>= 4.0)
      rspec-core
    erubi (1.10.0)
    excon (0.90.0)
    factory_bot (6.2.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    faker (2.19.0)
      i18n (>= 1.6, < 2)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    fernet (2.3)
      valcro (~> 0.1)
    ffi (1.15.5)
    ffprobe (0.1.0)
      hashie
    fog-aws (3.12.0)
      fog-core (~> 2.1)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
      ipaddress (~> 0.8)
    fog-core (2.2.4)
      builder
      excon (~> 0.71)
      formatador (~> 0.2)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.4)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (0.3.0)
    globalid (1.0.0)
      activesupport (>= 5.0)
    hash_diff (1.1.1)
    hashdiff (1.1.1)
    hashie (5.0.0)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    httparty (0.20.0)
      mime-types (~> 3.0)
      multi_xml (>= 0.5.2)
    i18n (1.8.11)
      concurrent-ruby (~> 1.0)
    i18n_data (0.16.0)
      simple_po_parser (~> 1.1)
    image_processing (1.12.1)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    interactor (3.1.2)
    interactor-rails (2.2.1)
      interactor (~> 3.0)
      rails (>= 4.2)
    io-wait (0.2.1)
    ipaddress (0.8.3)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.5.0)
    json (2.6.3)
    jsonapi-renderer (0.2.2)
    jwt (2.10.1)
      base64
    launchy (2.5.0)
      addressable (~> 2.7)
    letter_opener (1.8.1)
      launchy (>= 2.2, < 3)
    listen (3.7.1)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.13.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    mailgun-ruby (1.2.5)
      rest-client (>= 2.0.2)
    marcel (1.0.2)
    memory_profiler (1.0.0)
    mercadopago-sdk (2.1.0)
      json (~> 2.5)
      rest-client (~> 2.1)
    method_source (1.0.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    mini_magick (4.11.0)
    mini_mime (1.1.2)
    mini_portile2 (2.8.9)
    minitest (5.15.0)
    money (6.16.0)
      i18n (>= 0.6.4, <= 2)
    msgpack (1.4.4)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.4.1)
    net-http (0.6.0)
      uri
    net-imap (0.2.3)
      digest
      net-protocol
      strscan
    net-pop (0.1.1)
      digest
      net-protocol
      timeout
    net-protocol (0.1.2)
      io-wait
      timeout
    net-scp (3.0.0)
      net-ssh (>= 2.6.5, < 7.0.0)
    net-smtp (0.3.1)
      digest
      net-protocol
      timeout
    net-ssh (6.1.0)
    netrc (0.11.0)
    nio4r (2.5.8)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    oj (3.16.10)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    openssl (3.3.0)
    ostruct (0.6.1)
    pagy (5.9.3)
      activesupport
    parallel (1.25.1)
    parallel_tests (4.7.1)
      parallel
    paranoia (2.6.3)
      activerecord (>= 5.1, < 7.2)
    pg (1.4.4)
    phonelib (0.6.58)
    phony (2.19.11)
    premailer (1.16.0)
      addressable
      css_parser (>= 1.6.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.11.1)
      actionmailer (>= 3)
      premailer (~> 1.7, >= 1.7.9)
    public_suffix (4.0.6)
    puma (5.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-cors (1.1.1)
      rack (>= 2.0.0)
    rack-mini-profiler (2.3.3)
      rack (>= 1.2.0)
    rack-proxy (0.7.2)
      rack
    rack-test (1.1.0)
      rack (>= 1.0, < 3)
    rails (7.0.1)
      actioncable (= 7.0.1)
      actionmailbox (= 7.0.1)
      actionmailer (= 7.0.1)
      actionpack (= 7.0.1)
      actiontext (= 7.0.1)
      actionview (= 7.0.1)
      activejob (= 7.0.1)
      activemodel (= 7.0.1)
      activerecord (= 7.0.1)
      activestorage (= 7.0.1)
      activesupport (= 7.0.1)
      bundler (>= 1.15.0)
      railties (= 7.0.1)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.4.2)
      loofah (~> 2.3)
    rails-i18n (7.0.5)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    railties (7.0.1)
      actionpack (= 7.0.1)
      activesupport (= 7.0.1)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.0.6)
    ransack (2.5.0)
      activerecord (>= 5.2.4)
      activesupport (>= 5.2.4)
      i18n
    rb-fsevent (0.11.0)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redirect_safely (1.0.0)
      activemodel
    redis-client (0.25.1)
      connection_pool
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.3.6)
      strscan
    roo (2.9.0)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rspec-core (3.10.1)
      rspec-support (~> 3.10.0)
    rspec-expectations (3.10.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.10.0)
    rspec-mocks (3.10.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.10.0)
    rspec-rails (5.0.3)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      railties (>= 5.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.10.3)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    rubyzip (2.3.2)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    securerandom (0.4.1)
    semantic_range (3.0.0)
    sentry-rails (5.3.1)
      railties (>= 5.0)
      sentry-ruby-core (~> 5.3.1)
    sentry-ruby (5.3.1)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      sentry-ruby-core (= 5.3.1)
    sentry-ruby-core (5.3.1)
      concurrent-ruby
    shopify_api (14.8.0)
      activesupport
      concurrent-ruby
      hash_diff
      httparty
      jwt
      oj
      openssl
      securerandom
      sorbet-runtime
      zeitwerk (~> 2.5)
    shopify_app (22.5.2)
      activeresource
      addressable (~> 2.7)
      jwt (>= 2.2.3)
      rails (> 5.2.1)
      redirect_safely (~> 1.0)
      shopify_api (>= 14.7.0, < 15.0)
      sprockets-rails (>= 2.0.0)
    shoulda-matchers (5.1.0)
      activesupport (>= 5.2.0)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    simple_po_parser (1.1.6)
    simplecov (0.21.2)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.3)
    sixarm_ruby_unaccent (1.2.0)
    slack-notifier (2.4.0)
    sorbet-runtime (0.5.11954)
    spring (4.0.0)
    sprockets (4.0.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    sshkit (1.21.2)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    sshkit-interactive (0.3.0)
      sshkit (~> 1.12)
    ssrf_filter (1.2.0)
    stackprof (0.2.17)
    stripe (6.2.0)
    strscan (3.0.1)
    thor (1.2.1)
    tilt (2.0.10)
    timeout (0.2.0)
    tzinfo (2.0.4)
      concurrent-ruby (~> 1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    uri (1.0.3)
    valcro (0.2.1)
    web-console (4.2.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.23.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webpacker (5.4.4)
      activesupport (>= 5.2)
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xlsxtream (2.4.0)
      zip_tricks (>= 4.5, < 6)
    zeitwerk (2.5.3)
    zip_tricks (5.6.0)

GEM
  remote: https://rubygems.pkg.github.com/ThoughtCode/
  specs:
    mercately_mia_api (1.19.0)
      base64 (~> 0.2)
      faraday (~> 2.12)
      faraday-multipart (~> 1.1)
      fernet (~> 2.3)
      nokogiri (~> 1.18)

PLATFORMS
  ruby

DEPENDENCIES
  active_model_serializers (~> 0.10.13)
  active_storage_validations (~> 0.9.6)
  aws-sdk-s3 (~> 1.110)
  bootsnap (>= 1.4.4)
  byebug
  capistrano
  capistrano-bundler
  capistrano-rails
  capistrano-rails-console
  capistrano-rails-logs-tail (~> 1.0.5)
  capistrano-rvm
  capistrano-systemd-multiservice
  capistrano-yarn
  carrierwave (~> 2.2)
  carrierwave_backgrounder (~> 1.0.2)
  caxlsx_rails (~> 0.6.3)
  countries (~> 5.0)
  currency_select (~> 4.0)
  daemons (~> 1.4)
  delayed_job_active_record (~> 4.1)
  dotenv-rails
  dox
  factory_bot_rails
  faker
  ffprobe (~> 0.1.0)
  fog-aws (~> 3.12)
  httparty (~> 0.20.0)
  image_processing (~> 1.2)
  interactor-rails (~> 2.2, >= 2.2.1)
  jbuilder (~> 2.7)
  letter_opener (~> 1.8)
  listen (~> 3.3)
  mailgun-ruby (~> 1.2.5)
  memory_profiler
  mercadopago-sdk (~> 2.1)
  mercately_mia_api (= 1.19.0)!
  mini_magick (~> 4.11)
  pagy (~> 5.9.3)
  parallel_tests (~> 4.7, >= 4.7.1)
  paranoia (~> 2.4)
  pg (~> 1.4.4)
  phonelib (~> 0.6.58)
  phony (~> 2.19.11)
  premailer-rails (~> 1.11.1)
  puma (~> 5.0)
  rack-cors
  rack-mini-profiler (~> 2.0)
  rails (~> 7.0.1)
  rails-i18n (~> 7.0.1)
  ransack (~> 2.5)
  roo (~> 2.9)
  rspec-rails (~> 5.0.0)
  sass-rails (>= 6)
  sentry-rails (~> 5.3.1)
  sentry-ruby (~> 5.3.1)
  shopify_api (~> 14.8)
  shopify_app (~> 22.5)
  shoulda-matchers (~> 5.0)
  sidekiq (~> 7.0)
  simplecov
  slack-notifier (~> 2.4)
  spring
  stackprof
  stripe (~> 6.2)
  tzinfo-data
  web-console (>= 4.1.0)
  webmock
  webpacker (~> 5.0)
  xlsxtream (~> 2.4)

RUBY VERSION
   ruby 3.1.0p0

BUNDLED WITH
   2.3.3
