class CreateExportStatuses < ActiveRecord::Migration[7.0]
  def change
    create_table :export_statuses do |t|
      t.references :retailer, null: false, foreign_key: true
      t.string :export_type, null: false # 'summary' or 'detailed'
      t.string :status, null: false, default: 'in_progress' # 'in_progress', 'completed', 'failed'
      t.datetime :started_at, null: false
      t.datetime :completed_at
      t.text :error_message # Para guardar errores si falla

      t.timestamps
    end

    # Índices para consultas rápidas
    add_index :export_statuses, [:retailer_id, :export_type], name: 'index_export_statuses_on_retailer_and_type'
    add_index :export_statuses, [:retailer_id, :status], name: 'index_export_statuses_on_retailer_and_status'
    add_index :export_statuses, :started_at
  end
end
