# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_07_14_170441) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "btree_gin"
  enable_extension "plpgsql"
  enable_extension "unaccent"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "categories", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.bigint "retailer_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "web_id", null: false
    t.integer "order", default: 0
    t.index ["order"], name: "index_categories_on_order"
    t.index ["retailer_id"], name: "index_categories_on_retailer_id"
    t.index ["web_id"], name: "index_categories_on_web_id"
  end

  create_table "custom_answers", force: :cascade do |t|
    t.string "answer"
    t.string "custom_answerable_type", null: false
    t.bigint "custom_answerable_id", null: false
    t.bigint "custom_field_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["custom_answerable_type", "custom_answerable_id"], name: "index_custom_answers_on_custom_answerable"
    t.index ["custom_field_id"], name: "index_custom_answers_on_custom_field_id"
  end

  create_table "custom_fields", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.string "name"
    t.string "label"
    t.integer "kind", default: 0
    t.integer "made_for", default: 0
    t.boolean "mandatory_in_mercately", default: false
    t.boolean "mandatory_in_shops", default: false
    t.boolean "show_in_catalog", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "list_options", default: []
    t.boolean "active", default: true
    t.boolean "deleted", default: false
    t.index ["retailer_id"], name: "index_custom_fields_on_retailer_id"
  end

  create_table "customers", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "email"
    t.string "phone"
    t.string "web_id"
    t.boolean "required_update", default: false
    t.bigint "retailer_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "address"
    t.string "city"
    t.string "state"
    t.string "country_id"
    t.string "zip_code"
    t.string "id_type"
    t.string "id_number"
    t.string "web_id_in_mercately"
    t.string "id_in_mercately"
    t.boolean "ws_active", default: false
    t.index ["retailer_id"], name: "index_customers_on_retailer_id"
    t.index ["web_id"], name: "index_customers_on_web_id"
  end

  create_table "delayed_jobs", force: :cascade do |t|
    t.integer "priority", default: 0, null: false
    t.integer "attempts", default: 0, null: false
    t.text "handler", null: false
    t.text "last_error"
    t.datetime "run_at", precision: 6
    t.datetime "locked_at", precision: 6
    t.datetime "failed_at", precision: 6
    t.string "locked_by"
    t.string "queue"
    t.datetime "created_at", precision: 6
    t.datetime "updated_at", precision: 6
    t.index ["priority", "run_at"], name: "delayed_jobs_priority"
  end

  create_table "domain_files", force: :cascade do |t|
    t.string "certificate"
    t.string "certificate_key"
    t.bigint "retailer_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id"], name: "index_domain_files_on_retailer_id"
  end

  create_table "download_histories", force: :cascade do |t|
    t.string "type"
    t.string "url"
    t.bigint "retailer_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "download_type", default: 0
    t.index ["retailer_id"], name: "index_download_histories_on_retailer_id"
  end

  create_table "importations_loggers", force: :cascade do |t|
    t.integer "retailer_user_id"
    t.integer "retailer_id", null: false
    t.string "name"
    t.string "notification_email"
    t.string "file"
    t.string "original_file_name"
    t.jsonb "overwrite", default: {}, null: false
    t.jsonb "payload"
    t.jsonb "error_payload"
    t.jsonb "images_payload", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "importations_shopify_loggers", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.integer "retailer_user_id"
    t.string "domain"
    t.string "token"
    t.string "notification_email"
    t.jsonb "payload"
    t.jsonb "error_payload"
    t.jsonb "images_payload", default: {}
    t.boolean "import_completed", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id"], name: "index_importations_shopify_loggers_on_retailer_id"
  end

  create_table "order_item_variants", force: :cascade do |t|
    t.bigint "order_item_id", null: false
    t.bigint "product_variant_option_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["order_item_id"], name: "index_order_item_variants_on_order_item_id"
    t.index ["product_variant_option_id"], name: "index_order_item_variants_on_product_variant_option_id"
  end

  create_table "order_items", force: :cascade do |t|
    t.bigint "order_id"
    t.bigint "product_id"
    t.integer "quantity", default: 0
    t.decimal "unit_price", precision: 13, scale: 2, default: "0.0"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "processed", default: false
    t.boolean "sold_wholesale", default: false
    t.string "title"
    t.float "custom_tax", default: 0.0
    t.bigint "product_variant_combination_id"
    t.string "variant_name"
    t.integer "discount_type"
    t.decimal "discount_value", precision: 12, scale: 2, default: "0.0"
    t.string "discount_reason"
    t.decimal "total_discount", precision: 12, scale: 2, default: "0.0"
    t.index ["order_id"], name: "index_order_items_on_order_id"
    t.index ["product_id"], name: "index_order_items_on_product_id"
    t.index ["product_variant_combination_id"], name: "index_order_items_on_product_variant_combination_id"
  end

  create_table "orders", force: :cascade do |t|
    t.integer "status", default: 0
    t.decimal "subtotal", precision: 13, scale: 2, default: "0.0"
    t.decimal "tax_amount", precision: 10, scale: 2, default: "0.0"
    t.decimal "total", precision: 13, scale: 2, default: "0.0"
    t.bigint "customer_id"
    t.bigint "retailer_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "archived", default: false, null: false
    t.boolean "restore_stock", default: false, null: false
    t.integer "channel"
    t.string "web_id", null: false
    t.text "notes"
    t.string "agent_name"
    t.string "agent_id"
    t.integer "delivery_method"
    t.decimal "shipping_value", precision: 10, scale: 2, default: "0.0"
    t.string "address"
    t.string "city"
    t.string "state"
    t.string "zip_code"
    t.integer "origin"
    t.integer "record_type", default: 0
    t.string "sequential_number"
    t.text "terms_and_conditions"
    t.datetime "date_issued", precision: 6
    t.datetime "date_due", precision: 6
    t.string "currency"
    t.bigint "sales_channel_id"
    t.string "agent_email"
    t.text "description"
    t.string "country_id"
    t.string "latitude"
    t.string "longitude"
    t.string "transaction_id"
    t.bigint "shipping_cost_id"
    t.integer "payment_status", default: 1, null: false
    t.integer "discount_type"
    t.decimal "discount_value", precision: 12, scale: 2, default: "0.0"
    t.string "discount_reason"
    t.decimal "total_discount", precision: 12, scale: 2, default: "0.0"
    t.index ["customer_id"], name: "index_orders_on_customer_id"
    t.index ["retailer_id"], name: "index_orders_on_retailer_id"
    t.index ["sales_channel_id"], name: "index_orders_on_sales_channel_id"
    t.index ["shipping_cost_id"], name: "index_orders_on_shipping_cost_id"
  end

  create_table "payment_transactions", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.bigint "order_id", null: false
    t.bigint "customer_id", null: false
    t.decimal "amount", precision: 10, scale: 2, default: "0.0"
    t.string "card_id"
    t.string "transaction_id"
    t.integer "service", default: 0
    t.integer "status", default: 0
    t.string "description"
    t.string "currency", default: "USD"
    t.integer "card_exp_month"
    t.integer "card_exp_year"
    t.integer "card_last4"
    t.string "card_brand"
    t.string "card_funding"
    t.string "card_country"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "status_detail"
    t.string "note"
    t.string "attachment"
    t.integer "payment_method", default: 0, null: false
    t.string "custom_payment_method"
    t.index ["customer_id"], name: "index_payment_transactions_on_customer_id"
    t.index ["order_id"], name: "index_payment_transactions_on_order_id"
    t.index ["retailer_id"], name: "index_payment_transactions_on_retailer_id"
    t.index ["service"], name: "index_payment_transactions_on_service"
    t.index ["status"], name: "index_payment_transactions_on_status"
    t.index ["transaction_id"], name: "index_payment_transactions_on_transaction_id"
  end

  create_table "product_attachments", force: :cascade do |t|
    t.string "image"
    t.integer "index"
    t.bigint "product_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "serialized", default: true
    t.boolean "processed_jpg", default: true
    t.integer "file_size", default: 0
    t.string "shopify_id"
    t.string "public_image_url"
    t.string "woocommerce_id"
    t.index ["product_id"], name: "index_product_attachments_on_product_id"
  end

  create_table "product_variant_combinations", force: :cascade do |t|
    t.bigint "product_id"
    t.string "name"
    t.string "variant_option_web_ids", default: [], null: false, array: true
    t.decimal "price", precision: 12, scale: 2
    t.decimal "wholesale_price", precision: 12, scale: 2
    t.string "sku"
    t.integer "quantity", default: 0
    t.string "image"
    t.string "web_id", null: false
    t.datetime "deleted_at", precision: 6
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "track_quantity", default: true
    t.boolean "selling_without_stock", default: false
    t.string "shopify_id"
    t.string "inventory_name"
    t.integer "file_size", default: 0
    t.string "shopify_image_id"
    t.string "variant_url"
    t.string "public_image_url"
    t.string "woocommerce_id"
    t.index ["deleted_at"], name: "index_product_variant_combinations_on_deleted_at"
    t.index ["product_id"], name: "index_product_variant_combinations_on_product_id"
    t.index ["web_id"], name: "index_product_variant_combinations_on_web_id"
  end

  create_table "product_variant_options", force: :cascade do |t|
    t.string "name"
    t.bigint "product_variant_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "web_id"
    t.datetime "deleted_at", precision: 6
    t.string "shopify_id"
    t.string "woocommerce_id"
    t.index ["deleted_at"], name: "index_product_variant_options_on_deleted_at"
    t.index ["product_variant_id"], name: "index_product_variant_options_on_product_variant_id"
  end

  create_table "product_variants", force: :cascade do |t|
    t.string "name"
    t.bigint "product_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "web_id"
    t.datetime "deleted_at", precision: 6
    t.string "shopify_id"
    t.string "woocommerce_id"
    t.index ["deleted_at"], name: "index_product_variants_on_deleted_at"
    t.index ["product_id"], name: "index_product_variants_on_product_id"
  end

  create_table "products", force: :cascade do |t|
    t.string "title"
    t.string "description"
    t.string "sku"
    t.integer "quantity", default: 0
    t.decimal "price", precision: 12, scale: 2
    t.boolean "active", default: true
    t.bigint "subcategory_id"
    t.bigint "retailer_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false
    t.json "images"
    t.string "web_id", null: false
    t.integer "condition", default: 0
    t.integer "tracking_type", default: 0
    t.integer "type_tax"
    t.float "custom_tax"
    t.string "url"
    t.decimal "wholesale_price", default: "0.0"
    t.boolean "track_quantity", default: true
    t.boolean "selling_without_stock", default: false
    t.string "shopify_id"
    t.string "inventory_name"
    t.string "handle"
    t.string "preview_url"
    t.string "woocommerce_id"
    t.index ["retailer_id"], name: "index_products_on_retailer_id"
    t.index ["shopify_id"], name: "index_products_on_shopify_id", unique: true
    t.index ["subcategory_id"], name: "index_products_on_subcategory_id"
    t.index ["title"], name: "index_products_on_title", using: :gin
    t.index ["web_id"], name: "index_products_on_web_id"
  end

  create_table "regional_configs", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.string "currency", default: "usd"
    t.string "currency_symbol", default: "$"
    t.integer "number_of_decimals", default: 2
    t.string "thousands_separator", default: ","
    t.string "decimal_separator", default: "."
    t.string "web_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id"], name: "index_regional_configs_on_retailer_id"
    t.index ["web_id"], name: "index_regional_configs_on_web_id"
  end

  create_table "retailer_addresses", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "name"
    t.string "address"
    t.string "city"
    t.string "state"
    t.string "country"
    t.string "web_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id"], name: "index_retailer_addresses_on_retailer_id"
    t.index ["web_id"], name: "index_retailer_addresses_on_web_id"
  end

  create_table "retailer_daily_sales", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.date "calculation_date", null: false
    t.integer "order_count", default: 0, null: false
    t.decimal "sales_usd", precision: 13, scale: 2, default: "0.0"
    t.decimal "sales_default_currency", precision: 13, scale: 2, default: "0.0"
    t.string "orders_currency", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id", "calculation_date", "orders_currency"], name: "retailer_daily_sales_currency", unique: true
    t.index ["retailer_id"], name: "index_retailer_daily_sales_on_retailer_id"
  end

  create_table "retailer_schedules", force: :cascade do |t|
    t.bigint "retailer_id"
    t.integer "weekday", null: false
    t.string "web_id", null: false
    t.time "opening_time"
    t.time "closing_time"
    t.boolean "active", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id"], name: "index_retailer_schedules_on_retailer_id"
    t.index ["web_id"], name: "index_retailer_schedules_on_web_id"
  end

  create_table "retailer_users", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "email", default: "", null: false
    t.string "web_id"
    t.boolean "removed_from_team", default: false
    t.bigint "retailer_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id"], name: "index_retailer_users_on_retailer_id"
  end

  create_table "retailers", force: :cascade do |t|
    t.string "name"
    t.string "catalog_slug"
    t.string "unique_key"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "description"
    t.integer "category"
    t.string "currency"
    t.string "country"
    t.decimal "tax_amount", precision: 10, scale: 2, default: "0.0"
    t.string "timezone"
    t.string "logo_url"
    t.string "background_url"
    t.string "phone_number"
    t.string "facebook_url"
    t.string "instagram_url"
    t.string "twitter_url"
    t.string "whatsapp_url"
    t.string "tiktok_url"
    t.string "shop_main_color", default: "#21974E"
    t.string "font_color", default: "#FFFFFF"
    t.string "api_key"
    t.datetime "last_api_key_modified_date", precision: 6
    t.string "stripe_id"
    t.boolean "active", default: true
    t.integer "pending_orders_count", default: 0
    t.string "currency_code", default: "usd"
    t.boolean "active_send_order", default: true
    t.boolean "active_store_pickup", default: true
    t.boolean "rotate_images_in_catalog", default: false
    t.string "mp_access_token"
    t.string "mp_user_id"
    t.string "mp_refresh_token"
    t.string "mp_public_key"
    t.string "mp_site"
    t.string "mp_domain"
    t.boolean "payphone_connected", default: false
    t.string "domain"
    t.string "payphone_id"
    t.text "terms_conditions_content"
    t.integer "shipping_cost_method"
    t.boolean "mia_products_sync", default: false
    t.string "id_in_mercately"
    t.boolean "hide_product_prices", default: false
    t.index ["api_key"], name: "index_retailers_on_api_key"
    t.index ["domain"], name: "index_retailers_on_domain", unique: true, where: "((domain IS NOT NULL) AND ((domain)::text <> ''::text))"
  end

  create_table "sales_channels", force: :cascade do |t|
    t.bigint "retailer_id"
    t.string "title"
    t.string "web_id"
    t.integer "channel_type", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["retailer_id"], name: "index_sales_channels_on_retailer_id"
  end

  create_table "shipping_costs", force: :cascade do |t|
    t.bigint "retailer_id", null: false
    t.decimal "cost", precision: 10, scale: 2, comment: "Valor del envio para costo fijo"
    t.string "message", comment: "Mensaje para costo fijo"
    t.integer "shipping_type"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "location_name"
    t.boolean "is_active", default: true, null: false
    t.datetime "deleted_at", precision: 6
    t.index ["deleted_at"], name: "index_shipping_costs_on_deleted_at"
  end

  create_table "subcategories", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.bigint "category_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "web_id", null: false
    t.integer "products_count"
    t.integer "active_products_count", default: 0, null: false
    t.integer "order", default: 0
    t.index ["category_id"], name: "index_subcategories_on_category_id"
    t.index ["order"], name: "index_subcategories_on_order"
    t.index ["web_id"], name: "index_subcategories_on_web_id"
  end

  create_table "terms_and_conditions", force: :cascade do |t|
    t.string "terms_conditions_content"
    t.bigint "order_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["order_id"], name: "index_terms_and_conditions_on_order_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "custom_answers", "custom_fields"
  add_foreign_key "custom_fields", "retailers"
  add_foreign_key "download_histories", "retailers"
  add_foreign_key "importations_shopify_loggers", "retailers"
  add_foreign_key "order_item_variants", "order_items"
  add_foreign_key "order_item_variants", "product_variant_options"
  add_foreign_key "orders", "shipping_costs"
  add_foreign_key "payment_transactions", "customers"
  add_foreign_key "payment_transactions", "orders"
  add_foreign_key "payment_transactions", "retailers"
  add_foreign_key "product_variant_combinations", "products"
  add_foreign_key "regional_configs", "retailers"
  add_foreign_key "retailer_daily_sales", "retailers"
  add_foreign_key "shipping_costs", "retailers"
  add_foreign_key "terms_and_conditions", "orders"
end
