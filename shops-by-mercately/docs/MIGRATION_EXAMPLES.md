# Ejemplos de Migración a Sidekiq

Esta guía contiene ejemplos prácticos de cómo migrar diferentes tipos de jobs de DelayedJob a Sidekiq usando la infraestructura HybridJob.

## 📋 Índice de Ejemplos

1. [Sincronización de WooCommerce](#1-sincronización-de-woocommerce)
2. [Importación de Productos](#2-importación-de-productos)
3. [Envío de Notificaciones](#3-envío-de-notificaciones)
4. [Procesamiento de Webhooks](#4-procesamiento-de-webhooks)
5. [Jobs con Archivos](#5-jobs-con-archivos)

---

## 1. Sincronización de WooCommerce

### ❌ Antes (DelayedJob)

```ruby
# app/jobs/woocommerce_sync_job.rb
class WooCommerceSyncJob < ApplicationJob
  def perform(retailer_id, sync_type, options = {})
    retailer = Retailer.find(retailer_id)
    
    case sync_type
    when 'products'
      sync_products(retailer, options)
    when 'orders'
      sync_orders(retailer, options)
    end
  end
  
  private
  
  def sync_products(retailer, options)
    # lógica de sincronización
  end
end

# Encolado
WooCommerceSyncJob.delay(queue: 'integrations').perform(
  retailer.id, 
  'products', 
  { full_sync: true }
)
```

### ✅ Después (HybridJob)

```ruby
# app/jobs/integrations/woocommerce_sync_job.rb
module Integrations
  class WooCommerceSyncJob < HybridJob
    sidekiq_options queue: 'woocommerce_sync' if respond_to?(:sidekiq_options)
    
    def perform_job(retailer_id:, sync_type:, options: {})
      retailer = Retailer.find(retailer_id)
      
      case sync_type
      when 'products'
        sync_products(retailer, options)
      when 'orders'
        sync_orders(retailer, options)
      end
    end
    
    private
    
    def sync_products(retailer, options)
      # misma lógica de sincronización
    end
  end
end

# Encolado
QueueAdapterService.enqueue_job(
  Integrations::WooCommerceSyncJob,
  retailer,
  queue: :woocommerce_sync,
  retailer_id: retailer.id,
  sync_type: 'products',
  options: { full_sync: true }
)
```

### 🧪 Tests

```ruby
# spec/jobs/integrations/woocommerce_sync_job_spec.rb
RSpec.describe Integrations::WooCommerceSyncJob do
  let(:retailer) { create(:retailer) }
  let(:job) { described_class.new }
  
  describe '#perform_job' do
    context 'when syncing products' do
      it 'calls sync_products' do
        expect(job).to receive(:sync_products).with(retailer, { full_sync: true })
        
        job.perform_job(
          retailer_id: retailer.id,
          sync_type: 'products',
          options: { full_sync: true }
        )
      end
    end
  end
end
```

---

## 2. Importación de Productos

### ❌ Antes (DelayedJob)

```ruby
class ProductImportJob < ApplicationJob
  def perform(retailer_id, file_path, import_type)
    retailer = Retailer.find(retailer_id)
    
    case import_type
    when 'csv'
      import_from_csv(retailer, file_path)
    when 'excel'
      import_from_excel(retailer, file_path)
    end
    
    # Cleanup
    File.delete(file_path) if File.exist?(file_path)
  end
end

# Encolado
ProductImportJob.delay.perform(retailer.id, '/tmp/products.csv', 'csv')
```

### ✅ Después (HybridJob)

```ruby
# app/jobs/imports/product_import_job.rb
module Imports
  class ProductImportJob < HybridJob
    sidekiq_options queue: 'imports' if respond_to?(:sidekiq_options)
    
    def perform_job(retailer_id:, file_path:, import_type:)
      retailer = Retailer.find(retailer_id)
      
      begin
        case import_type
        when 'csv'
          import_from_csv(retailer, file_path)
        when 'excel'
          import_from_excel(retailer, file_path)
        end
      ensure
        # Cleanup
        File.delete(file_path) if File.exist?(file_path)
      end
    end
    
    private
    
    def import_from_csv(retailer, file_path)
      # lógica de importación CSV
    end
    
    def import_from_excel(retailer, file_path)
      # lógica de importación Excel
    end
  end
end

# Encolado
QueueAdapterService.enqueue_job(
  Imports::ProductImportJob,
  retailer,
  queue: :imports,
  retailer_id: retailer.id,
  file_path: '/tmp/products.csv',
  import_type: 'csv'
)
```

---

## 3. Envío de Notificaciones

### ❌ Antes (DelayedJob)

```ruby
class NotificationJob < ApplicationJob
  def perform(user_id, notification_type, data)
    user = User.find(user_id)
    
    case notification_type
    when 'email'
      send_email(user, data)
    when 'sms'
      send_sms(user, data)
    when 'push'
      send_push(user, data)
    end
  end
end

# Encolado
NotificationJob.delay(queue: 'notifications').perform(
  user.id, 
  'email', 
  { subject: 'Welcome', body: 'Hello!' }
)
```

### ✅ Después (HybridJob)

```ruby
# app/jobs/notifications/notification_job.rb
module Notifications
  class NotificationJob < HybridJob
    sidekiq_options queue: 'default' if respond_to?(:sidekiq_options)
    
    def perform_job(user_id:, notification_type:, data:)
      user = User.find(user_id)
      
      case notification_type
      when 'email'
        send_email(user, data)
      when 'sms'
        send_sms(user, data)
      when 'push'
        send_push(user, data)
      end
    end
    
    private
    
    def send_email(user, data)
      # lógica de email
    end
  end
end

# Encolado
QueueAdapterService.enqueue_job(
  Notifications::NotificationJob,
  current_retailer, # o cualquier retailer relevante
  user_id: user.id,
  notification_type: 'email',
  data: { subject: 'Welcome', body: 'Hello!' }
)
```

---

## 4. Procesamiento de Webhooks

### ❌ Antes (DelayedJob)

```ruby
class WebhookProcessorJob < ApplicationJob
  def perform(webhook_data, source, retailer_id)
    retailer = Retailer.find(retailer_id)
    
    case source
    when 'shopify'
      process_shopify_webhook(webhook_data, retailer)
    when 'woocommerce'
      process_woocommerce_webhook(webhook_data, retailer)
    end
  end
end

# Encolado desde controller
WebhookProcessorJob.delay.perform(params.to_h, 'shopify', retailer.id)
```

### ✅ Después (HybridJob)

```ruby
# app/jobs/webhooks/webhook_processor_job.rb
module Webhooks
  class WebhookProcessorJob < HybridJob
    sidekiq_options queue: 'default' if respond_to?(:sidekiq_options)
    
    def perform_job(webhook_data:, source:, retailer_id:)
      retailer = Retailer.find(retailer_id)
      
      case source
      when 'shopify'
        process_shopify_webhook(webhook_data, retailer)
      when 'woocommerce'
        process_woocommerce_webhook(webhook_data, retailer)
      end
    end
    
    private
    
    def process_shopify_webhook(data, retailer)
      # lógica específica de Shopify
    end
  end
end

# Encolado desde controller
QueueAdapterService.enqueue_job(
  Webhooks::WebhookProcessorJob,
  retailer,
  webhook_data: params.to_h,
  source: 'shopify',
  retailer_id: retailer.id
)
```

---

## 5. Jobs con Archivos

### ❌ Antes (DelayedJob)

```ruby
class FileProcessorJob < ApplicationJob
  def perform(file_id, processing_type)
    uploaded_file = UploadedFile.find(file_id)
    
    case processing_type
    when 'resize_images'
      resize_images(uploaded_file)
    when 'generate_thumbnails'
      generate_thumbnails(uploaded_file)
    end
  end
end

# Encolado
FileProcessorJob.delay(queue: 'file_processing').perform(file.id, 'resize_images')
```

### ✅ Después (HybridJob)

```ruby
# app/jobs/files/file_processor_job.rb
module Files
  class FileProcessorJob < HybridJob
    sidekiq_options queue: 'default' if respond_to?(:sidekiq_options)
    
    def perform_job(file_id:, processing_type:, retailer_id:)
      uploaded_file = UploadedFile.find(file_id)
      retailer = Retailer.find(retailer_id)
      
      case processing_type
      when 'resize_images'
        resize_images(uploaded_file)
      when 'generate_thumbnails'
        generate_thumbnails(uploaded_file)
      end
    end
    
    private
    
    def resize_images(file)
      # lógica de redimensionamiento
    end
  end
end

# Encolado
QueueAdapterService.enqueue_job(
  Files::FileProcessorJob,
  current_retailer,
  file_id: file.id,
  processing_type: 'resize_images',
  retailer_id: current_retailer.id
)
```

---

## 🔧 Patrones Comunes de Migración

### 1. Conversión de Argumentos

```ruby
# Antes: argumentos posicionales
def perform(arg1, arg2, arg3)

# Después: keyword arguments
def perform_job(arg1:, arg2:, arg3:)
```

### 2. Manejo de Objetos Complejos

```ruby
# ❌ No serializable
QueueAdapterService.enqueue_job(MyJob, retailer, user_object: user)

# ✅ Serializable
QueueAdapterService.enqueue_job(MyJob, retailer, user_id: user.id)
```

### 3. Configuración de Colas

```ruby
# Siempre especificar la cola apropiada
sidekiq_options queue: 'nombre_cola' if respond_to?(:sidekiq_options)
```

### 4. Testing

```ruby
# Testear el método perform_job directamente
job = MyJob.new
result = job.perform_job(param1: 'value1', param2: 'value2')
```

## 🚨 Errores Comunes

1. **Olvidar keyword arguments:** Sidekiq serializa como hash
2. **Pasar objetos complejos:** Solo tipos básicos (String, Integer, Hash, Array)
3. **No especificar cola:** Usar `queue: :nombre_cola` en enqueue_job
4. **No testear ambos adapters:** Probar con Sidekiq y DelayedJob

## ✅ Checklist de Migración

- [ ] Convertir argumentos a keyword arguments
- [ ] Heredar de HybridJob
- [ ] Configurar sidekiq_options
- [ ] Usar QueueAdapterService.enqueue_job
- [ ] Actualizar tests
- [ ] Probar con feature flag activado/desactivado
- [ ] Verificar logs
- [ ] Documentar cambios
