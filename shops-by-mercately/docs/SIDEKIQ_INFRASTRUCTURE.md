# Infraestructura de Sidekiq para Shops by Mercately

Esta documentación describe la infraestructura base de Sidekiq implementada para permitir la migración gradual de DelayedJob a Sidekiq en el proyecto Shops by Mercately.

## 🎯 Objetivo

Proporcionar una infraestructura robusta que permita a los equipos migrar gradualmente sus integraciones de DelayedJob a Sidekiq usando feature flags, sin afectar la funcionalidad existente.

## 📋 Componentes de la Infraestructura

### 1. HybridJob - Clase Base Universal

**Ubicación:** `app/jobs/hybrid_job.rb`

Clase base que permite que los jobs funcionen tanto con Sidekiq como con DelayedJob de manera transparente.

```ruby
class MyIntegrationJob < HybridJob
  # Configurar cola específica (opcional)
  sidekiq_options queue: 'integrations' if respond_to?(:sidekiq_options)
  
  def perform_job(retailer_id:, data:)
    # Tu lógica aquí
    retailer = Retailer.find(retailer_id)
    # ... procesamiento
  end
end
```

**Características:**
- ✅ Maneja argumentos posicionales y keyword arguments
- ✅ Serialización automática para Sidekiq
- ✅ Compatibilidad con DelayedJob
- ✅ Logging integrado

### 2. QueueAdapterService - Enrutador de Colas

**Ubicación:** `app/services/queue_adapter_service.rb`

Servicio que decide automáticamente si usar Sidekiq o DelayedJob basado en feature flags.

```ruby
# Encolar un job
QueueAdapterService.enqueue_job(
  MyIntegrationJob,
  retailer,
  queue: :integrations,
  retailer_id: retailer.id,
  data: { sync_type: 'full' }
)
```

**Características:**
- ✅ Feature flags por retailer
- ✅ Detección automática de colas
- ✅ Logging detallado
- ✅ Estadísticas de colas

### 3. Configuración de Sidekiq

**Archivos:**
- `config/sidekiq.yml` - Configuración de colas y workers
- `config/initializers/sidekiq.rb` - Configuración de Redis y opciones
- `config/routes.rb` - Sidekiq Web interface

## 🚀 Cómo Migrar una Integración

### Paso 1: Convertir tu Job a HybridJob

**Antes (DelayedJob):**
```ruby
class WooCommerceSyncJob < ApplicationJob
  def perform(retailer_id, sync_type)
    # lógica
  end
end

# Encolado
WooCommerceSyncJob.delay.perform(retailer.id, 'products')
```

**Después (HybridJob):**
```ruby
class WooCommerceSyncJob < HybridJob
  sidekiq_options queue: 'woocommerce_sync' if respond_to?(:sidekiq_options)
  
  def perform_job(retailer_id:, sync_type:)
    # misma lógica, pero con keyword arguments
  end
end

# Encolado
QueueAdapterService.enqueue_job(
  WooCommerceSyncJob,
  retailer,
  queue: :woocommerce_sync,
  retailer_id: retailer.id,
  sync_type: 'products'
)
```

### Paso 2: Configurar Feature Flag

```bash
# Para habilitar Sidekiq para retailers específicos
export USE_SIDEKIQ_FOR_RETAILER="1,2,3"
```

### Paso 3: Testing

```ruby
# spec/jobs/woo_commerce_sync_job_spec.rb
RSpec.describe WooCommerceSyncJob do
  let(:retailer) { create(:retailer) }
  
  describe '#perform_job' do
    it 'syncs products successfully' do
      job = WooCommerceSyncJob.new
      result = job.perform_job(
        retailer_id: retailer.id,
        sync_type: 'products'
      )
      
      expect(result).to be_successful
    end
  end
end
```

## 🔧 Configuración de Colas

### Colas Disponibles

```yaml
# config/sidekiq.yml
:queues:
  - exports           # Exportaciones (alta prioridad)
  - imports           # Importaciones (alta prioridad)  
  - woocommerce_sync  # Sincronización WooCommerce
  - shopify_sync      # Sincronización Shopify
  - mia_sync          # Sincronización MIA
  - default           # Jobs generales
```

### Agregar Nueva Cola

1. **Agregar a sidekiq.yml:**
```yaml
:queues:
  - mi_nueva_cola
```

2. **Agregar a QueueAdapterService:**
```ruby
QUEUES = {
  mi_nueva_cola: 'mi_nueva_cola',
  # ... otras colas
}.freeze
```

3. **Usar en tu job:**
```ruby
class MiNuevoJob < HybridJob
  sidekiq_options queue: 'mi_nueva_cola' if respond_to?(:sidekiq_options)
end
```

## 📊 Monitoreo

### Sidekiq Web Interface

Accede a `/sidekiq` para monitorear:
- Jobs en cola
- Jobs procesados
- Jobs fallidos
- Estadísticas en tiempo real

**Credenciales de producción:**
```bash
export SIDEKIQ_USERNAME="admin"
export SIDEKIQ_PASSWORD="tu_password_seguro"
```

### Estadísticas Programáticas

```ruby
# Obtener estadísticas de ambas colas
stats = QueueAdapterService.queue_stats
# => {
#   delayed_job: { pending: 5, working: 2, failed: 1 },
#   sidekiq: { pending: 10, working: 3, failed: 0 }
# }
```

## 🚨 Troubleshooting

### Problemas Comunes

1. **Job no se ejecuta en Sidekiq:**
   - Verificar que Redis esté corriendo
   - Verificar configuración de `USE_SIDEKIQ_FOR_RETAILER`
   - Revisar logs de Sidekiq

2. **Argumentos no se serializan correctamente:**
   - Usar keyword arguments en `perform_job`
   - Evitar objetos complejos, usar IDs

3. **Cola no existe:**
   - Verificar que la cola esté en `sidekiq.yml`
   - Verificar que esté en `QueueAdapterService::QUEUES`

### Logs Útiles

```bash
# Logs de Sidekiq
tail -f log/sidekiq.log

# Logs de la aplicación con filtro
tail -f log/development.log | grep "QUEUE-ADAPTER\|SIDEKIQ"
```

## 🔄 Deployment

### Capistrano

La configuración de deploy ya incluye restart automático de Sidekiq:

```ruby
# config/deploy.rb
after :restart_delayed_job, :restart_sidekiq
```

### Systemd Service

Asegúrate de tener configurado el servicio `mercately-sidekiq` en systemd.

## 📝 Próximos Pasos

1. **Migrar integraciones existentes** una por una
2. **Monitorear performance** y ajustar concurrencia
3. **Gradualmente eliminar DelayedJob** cuando todas las integraciones estén migradas
4. **Optimizar configuración** basado en métricas de producción

## 🤝 Contribuir

Para agregar nuevas funcionalidades a la infraestructura:

1. Crear tests para la nueva funcionalidad
2. Actualizar esta documentación
3. Probar con al menos una integración real
4. Solicitar code review del equipo de infraestructura
