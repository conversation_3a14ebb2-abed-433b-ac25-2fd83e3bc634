class KafkaCampaignAnalyzer
  attr_reader :start_date, :retailer_ids

  def initialize(start_date = '2025-07-31')
    @start_date = start_date
    @retailer_ids = load_retailer_ids
  end

  def load_retailer_ids
    Retailer.where(kafka_enabled: true)
            .joins(:campaigns)
            .where('campaigns.created_at >= ?', start_date)
            .distinct
            .pluck(:id)
            .sort
  end

  def print_header
    puts "🔍 ANÁLISIS DETALLADO POR RETAILER DESDE 01/08/2025"
    puts "=" * 80
    puts "📊 Analizando #{retailer_ids.count} retailers con kafka_enabled=true y campañas desde #{Date.parse(start_date).strftime('%d/%m/%Y')}"
    puts "🎯 Retailers encontrados: #{retailer_ids.join(', ')}"
  end

# También mostrar retailers con kafka_enabled pero SIN campañas (para referencia)
retailers_without_campaigns = Retailer.where(kafka_enabled: true)
                                     .where.not(id: retailer_ids)
                                     .pluck(:id, :name)

if retailers_without_campaigns.any?
  puts "\n📝 Retailers con kafka_enabled=true pero SIN campañas desde #{start_date.strftime('%d/%m/%Y')}:"
  retailers_without_campaigns.each do |id, name|
    puts "   💤 #{id} - #{name}"
  end
end

retailer_ids.each_with_index do |retailer_id, index|
  puts "\n" + "="*60
  puts "#{index + 1}/#{retailer_ids.count} - RETAILER ID: #{retailer_id}"
  puts "="*60
  
  retailer = Retailer.find_by(id: retailer_id)
  
  if retailer.nil?
    puts "❌ Retailer no encontrado"
    next
  end
  
  puts "🏪 #{retailer.name}"
  puts "🔧 kafka_enabled: #{retailer.kafka_enabled}"
  
  # Filtrar campañas desde 01/08/2025
  campaigns = retailer.campaigns.where('created_at >= ?', start_date)
  puts "📊 Total campañas desde 01/08: #{campaigns.count}"
  
  kafka_count = campaigns.where(sender_strategy: 'kafka').count
  sync_count = campaigns.where(sender_strategy: 'synchronous').count
  nil_count = campaigns.where(sender_strategy: nil).count
  kafka_adoption = campaigns.count > 0 ? (kafka_count.to_f / campaigns.count * 100).round(1) : 0
  
  puts "🎯 Adopción Kafka: #{kafka_adoption}% (#{kafka_count}/#{campaigns.count})"
  puts "   ✅ Kafka: #{kafka_count} | ⚠️ Sync: #{sync_count} | ❓ Nil: #{nil_count}"
  
  # Estados
  status_counts = campaigns.group(:status).count
  puts "📊 Estados: #{status_counts.map { |k,v| "#{k}:#{v}" }.join(', ')}"
  
  # Últimas 5 campañas
  puts "📋 Últimas 5 campañas desde 01/08:"
  campaigns.order(:created_at).last(5).each do |c|
    strategy_icon = case c.sender_strategy
                   when 'kafka' then '✅'
                   when 'synchronous' then '⚠️'
                   else '❓'
                   end
    status_icon = c.status == 'sent' ? '✅' : c.status == 'processing' ? '🔄' : '❌'
    date_str = c.created_at.strftime('%d/%m %H:%M')
    puts "   #{strategy_icon}#{status_icon} #{c.id} | #{c.sender_strategy || 'nil'} | #{c.status} | #{date_str} | #{c.name[0..25]}..."
  end
  
  # Análisis de problemas específicos
  processing_campaigns = campaigns.where(status: 'processing')
  if processing_campaigns.any?
    puts "🔄 #{processing_campaigns.count} campañas en PROCESSING:"
    processing_campaigns.each do |c|
      puts "   🔄 #{c.id} | #{c.web_id} | #{c.name[0..30]}... | #{c.created_at.strftime('%d/%m %H:%M')}"
    end
  end
  
  if sync_count > 0
    puts "🔍 #{sync_count} campañas sincrónicas - investigar por qué no usaron Kafka"
  end
  
  if nil_count > 0
    puts "❓ #{nil_count} campañas sin sender_strategy definido"
  end
  
  problematic = campaigns.where.not(status: 'sent')
  if problematic.any?
    puts "🚨 #{problematic.count} campañas problemáticas: #{problematic.pluck(:status).uniq.join(', ')}"
  end
  
  # Recomendación
  if kafka_adoption < 50
    puts "🔴 CRÍTICO: Adopción muy baja (#{kafka_adoption}%)"
  elsif kafka_adoption < 80
    puts "🟡 MEJORABLE: Adopción moderada (#{kafka_adoption}%)"
  else
    puts "🟢 BIEN: Buena adopción (#{kafka_adoption}%)"
  end
end

puts "\n📈 RESUMEN RÁPIDO DESDE 01/08:"
retailer_ids.each do |id|
  retailer = Retailer.find_by(id: id)
  next unless retailer&.kafka_enabled
  
  campaigns = retailer.campaigns.where('created_at >= ?', start_date)
  next if campaigns.empty?
  
  kafka_count = campaigns.where(sender_strategy: 'kafka').count
  processing_count = campaigns.where(status: 'processing').count
  adoption = (kafka_count.to_f / campaigns.count * 100).round(1)
  
  icon = adoption >= 80 ? "🟢" : adoption >= 50 ? "🟡" : "🔴"
  processing_icon = processing_count > 0 ? " 🔄#{processing_count}" : ""
  puts "#{icon} #{retailer.name}: #{adoption}% (#{kafka_count}/#{campaigns.count})#{processing_icon}"
end

puts "\n🎯 CAMPAÑAS EN PROCESSING DESDE 31/07:"
Campaign.joins(:retailer)
        .where('campaigns.created_at >= ?', start_date)
        .where(status: 'processing')
        .where(retailers: { id: retailer_ids })
        .each do |c|
  puts "🔄 #{c.retailer.name} | #{c.id} | #{c.web_id} | #{c.name[0..40]}... | #{c.created_at.strftime('%d/%m %H:%M')}"
end

puts "\n🏁 ANÁLISIS COMPLETADO"
puts "📊 Total retailers analizados: #{retailer_ids.count}"
puts "💤 Retailers con kafka_enabled pero sin campañas: #{retailers_without_campaigns.count}"
