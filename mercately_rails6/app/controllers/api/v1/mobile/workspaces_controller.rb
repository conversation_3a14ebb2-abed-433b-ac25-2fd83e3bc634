# frozen_string_literal: true

module Api
  module V1
    module Mobile
      class WorkspacesController < Api::MobileController
        include CurrentRetailer

        def index
          workspaces = current_retailer_user.user.retailers

          if workspaces.present?
            render json: { workspaces: serialize_workspaces(workspaces) }, status: :ok
          else
            render json: { workspaces: [] }, status: :ok
          end
        end

        def change
          user = current_retailer_user.user
          retailer_user = RetailerUser.find_by(
            retailer_id: change_params[:retailer_id],
            user_id: user.id
          )

          if retailer_user.present?
            retailer_user.current_mobile!
            render json: { message: "Has cambiado a #{retailer_user.retailer.name}" }, status: :ok
          else
            render json: { error: 'Workspace no encontrado' }, status: :ok
          end
        rescue StandardError => e
          Rails.logger.error "Error changing workspace: #{e.message}"
          render json: { error: 'Something went wrong' }, status: :internal_server_error
        end

        private

          def serialize_workspaces(workspaces)
            ::Api::V1::Mobile::WorkspaceSerializer.new(workspaces).serializable_hash[:data].pluck(:attributes)
          end

          def change_params
            params.permit(:retailer_id)
          end
      end
    end
  end
end
