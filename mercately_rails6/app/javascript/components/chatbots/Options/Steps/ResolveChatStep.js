import React from "react";
import { useTranslation } from 'react-i18next';

import CloseIcon from '../../../icons/CloseIcon';
import ResolveChatIcon from "../../../icons/ResolveChatIcon";

const ResolveChatStep = ({ handleCancelOption }) => {
  const { t } = useTranslation();

  return (
    <div className="col-md-12 mt-20">
      <div className="figma-field mb-0 pl-0">
        <div className="fz-14 my-20 text-right mr-20">
          <a type="button" onClick={handleCancelOption}>
            <CloseIcon className="fill-dark" />
          </a>
        </div>
        <div className="mb-56 bt-16 mx-40 text-center">
          <h3 className="fz-24 ff-semi-bold">{t('chatbot.config.no_active_mia.title')}</h3>
          <p className="fz-14">{t('chatbot.config.no_active_mia.description')}</p>
          <div className="pb-20">
            <ResolveChatIcon />
            {/* <img src={BotIcon} alt="Chatbot" className="img-fluid position-relative h-158"  /> */}
          </div>

          <button type="button" className="border-12 btn btn-outline-blue px-78" onClick={() => handleCancelOption()}>
            {t('chatbot.config.no_active_mia.ok_button')}
          </button>
        </div>
      </div>
    </div>
  )
}

export default ResolveChatStep;
