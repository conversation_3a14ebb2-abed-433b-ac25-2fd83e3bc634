/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable import/no-unresolved */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import ReactPaginate from 'react-paginate';
import { isEmpty } from 'lodash';
import ZoomIcon from 'images/zoom.svg';
import FilterIcon from 'images/filter.svg';

import { fetchOrders, getPendingOrder, fetchExportStatus, startOrderExport, startDetailedOrderExport } from '../../actions/shops/orders';
import { getCurrentRetailerUserInfo, getRetailerInfo } from '../../actions/retailerUsersActions';

import SidebarModal from '../shared/SidebarModal';
import FilterOrdersForm from './FilterOrdersForm';
import SidebarStore from '../shared/SidebarStore';
import httpServiceShops from '../../services/httpServiceShops';
import SidebarStoreContent from '../shared/SidebarStore/content/SidebarStoreContent';
import OrderTableList from './OrderTableList';

const Orders = () => {
  console.log('🚀 [FRONTEND] Orders component loaded - CLEAN BRANCH!');
  const dispatch = useDispatch();

  // Redux-based polling para export status
  useEffect(() => {
    console.log('🔍 [REDUX POLLING] Starting Redux-based polling...');

    const checkExportStatus = () => {
      console.log('🔍 [REDUX POLLING] Dispatching fetchExportStatus...');
      dispatch(fetchExportStatus());
    };

    checkExportStatus(); // Llamada inicial
    const interval = setInterval(checkExportStatus, 10000); // Cada 10 segundos

    return () => {
      console.log('🔍 [REDUX POLLING] Cleaning up polling...');
      clearInterval(interval);
    };
  }, [dispatch]);
  const {
    orders = [],
    total_pages = 0,
    orderLoading,
    exportStatus = { exporting: false, summary: {}, detailed: {} }
  } = useSelector((reduxState) => reduxState.mainReducer);
  const { totalPendindOrders } = useSelector((reduxState) => reduxState.ordersReducer);

  // Log cuando cambie el exportStatus
  useEffect(() => {
    console.log('📊 [REDUX STATE] Export status updated:', exportStatus);
  }, [exportStatus]);

  // Helper functions para el estado del botón de exportar
  const isExporting = () => {
    return exportStatus.exporting ||
           exportStatus.summary?.in_progress ||
           exportStatus.detailed?.in_progress;
  };

  const hasReadyDownload = () => {
    return exportStatus.summary?.ready || exportStatus.detailed?.ready;
  };

  const getExportButtonText = () => {
    if (isExporting()) return "Exportando...";
    if (hasReadyDownload()) return "Descargar";
    return "Exportar";
  };

  const getExportButtonIcon = () => {
    if (isExporting()) return "fas fa-spinner fa-spin ml-8";
    if (hasReadyDownload()) return "fas fa-download ml-8";
    return "fas fa-caret-down ml-8";
  };

  const isExportDisabled = () => {
    return isExporting();
  };
  const [page, setPage] = useState(0);
  const [showSection, setShowSection] = useState('orders');
  const [searchText, setSearchText] = useState('');
  const [search, setSearch] = useState({
    "q[customer_full_name_cont]": '',
    "q[sequential_number_end]": '',
    "q[agent_id_eq]": '',
    "q[status_eq]": '',
    "q[created_at_gteq]": '',
    "q[created_at_lteq]": ''
  });

  const prevShowSectionRef = useRef();
  const reloadOrderRef = useRef();

  useEffect(() => {
    prevShowSectionRef.current = showSection;
    reloadOrderRef.current = false;

    dispatch(getCurrentRetailerUserInfo());
    dispatch(getRetailerInfo());

    return () => dispatch({ type: 'RESET_TEMPORAL_STATE' });
  }, []);

  useEffect(() => {
    if (page === 0) return;

    // eslint-disable-next-line no-undef
    const params = _.omitBy({ page, ...search }, _.isNil);

    dispatch(fetchOrders(params));
  }, [page]);

  useEffect(() => {
    const status = sectionToShow();
    setSearch({
      ...search,
      "q[status_eq]": status
    });
  }, [showSection]);

  useEffect(() => {
    if (prevShowSectionRef.current !== showSection || reloadOrderRef.current) {
      prevShowSectionRef.current = showSection;

      // eslint-disable-next-line no-undef
      const params = _.omitBy({ page, ...search }, _.isNil);
      if (page !== 1) {
        setPage(1);
      } else {
        dispatch(fetchOrders(params));
      }
    }
  }, [search]);

  useEffect(() => {
    reloadOrderRef.current = false;
    if (searchText === '') {
      reloadOrderRef.current = true;
      searchOrder();
    }
  }, [searchText]);

  useEffect(() => {
    if (!isEmpty(orders)) {
      dispatch(getPendingOrder());
    }
  }, [orders]);

  const sectionToShow = () => {
    switch (showSection) {
      case 'successful_orders':
        return 2;
      case 'pending_orders':
        return 0;
      case 'cancelled_orders':
        return 1;
      default:
        return null;
    }
  };

  const toggleTabs = (e) => {
    const el = e.target;
    document.querySelectorAll('.select-orders').forEach((label) => {
      label.parentNode.classList.remove('selected-tab');
    });
    document.querySelector(`label[for=${el.id}]`).parentNode.classList.add('selected-tab');
    setShowSection(el.value);
  };

  const handlePageClick = (e) => {
    setPage(e.selected + 1);
  };

  const searchOrder = (filters = {}) => {
    reloadOrderRef.current = true;
    setSearch({
      ...search,
      ...filters,
      "q[customer_full_name_cont]": searchText
    });
  };

  const handleSearch = (filters) => {
    searchOrder(filters);
  };

  const onKeyPress = (e) => {
    if (e.which === 13) {
      e.preventDefault();
      searchOrder();
    }
  };

  const exportFilteredOrder = () => {
    if (isExportDisabled()) return; // Prevenir clicks durante export

    if (confirm('¿Estás seguro de exportar órdenes con los filtros seleccionados?')) {
      console.log('🚀 [EXPORT] Starting summary export...');
      dispatch(startOrderExport(search));
    }
  };

  const exportFilteredDetailedOrder = () => {
    if (isExportDisabled()) return; // Prevenir clicks durante export

    if (confirm('¿Estás seguro de exportar detalle de órdenes con los filtros seleccionados?')) {
      console.log('🚀 [EXPORT] Starting detailed export...');
      dispatch(startDetailedOrderExport(search));
    }
  };

  return (
    <div className="ml-sm-61 mr-sm-10 no-left-margin-xs">
      <SidebarModal id="order_filters" title="Filtros">
        <FilterOrdersForm handleSearch={handleSearch} />
      </SidebarModal>
      <div className="container-fluid-no-padding">
        <div className="row">
          <div className="d-flex">
            <SidebarStore>
              <SidebarStoreContent place="orders" />
            </SidebarStore>
          </div>
          <div className="col bg-white px-24 store-content pt-10">
            <div className="row align-items-center">
              <div className="col col-sm-4 mt-12">
                <h1 className="page__title">Órdenes</h1>
              </div>
              <div className="col t-right">
                <div className="mr-20 d-inline-block">
                  <span
                    className={`dropdown__button btn-funnel-action fz-16 ${isExportDisabled() ? 'bg-gray cursor-not-allowed' : 'bg-light-gray'}`}
                    tabIndex={isExportDisabled() ? "-1" : "0"}
                    style={{
                      opacity: isExportDisabled() ? 0.6 : 1,
                      border: isExportDisabled() ? '1px solid #e5e7eb' : 'none'
                    }}
                  >
                    {getExportButtonText()}
                    <i className={getExportButtonIcon()}></i>
                  </span>
                  {!isExportDisabled() && (
                    <ul className="dropdown__menu actions-funnel__menu actions__menu" style={{ zIndex: 5 }}>
                      <li className="t-left" onClick={exportFilteredOrder}>
                        Resumen de Órdenes a Excel
                      </li>
                      <li className="t-left" onClick={exportFilteredDetailedOrder} style={{ whiteSpace: "break-spaces" }}>
                        Detalle de Órdenes con productos a Excel
                      </li>
                    </ul>
                  )}
                </div>
                <div className="d-inline">
                  <Link className="blue-button my-24px my-md-0 text-decoration-none" to={`/retailers/${ENV.SLUG}/orders/new`}>
                    + Crear orden
                  </Link>
                </div>
              </div>
            </div>

            <div className="col-md-12 d-flex align-items-center px-0">
              <div className="mr-12 search-input pl-0">
                <img src={ZoomIcon} className="search-input-loop" />
                <input
                  className="search-input-new"
                  placeholder="Buscar orden por nombre del cliente"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  onKeyPress={onKeyPress}
                  style={{ width: 325 }}
                />
              </div>
              <button className="gray-light-button mr-12 my-24px my-md-0 w-100 w-sm-auto" type="button" data-toggle="modal" data-target="#order_filters">
                Filtros
                <img src={FilterIcon} className="pl-8" />
              </button>
            </div>

            <div className="order-table-container mt-20">
              <div className="hide-on-tablet-and-down col-12 px-0">
                <div style={{ borderBottom: '2px solid #DDD' }}>
                  <div className="d-inline-block tab bg-transparent selected-tab">
                    <label className="select-orders" htmlFor="show_orders">
                      Todas
                    </label>
                  </div>
                  <div
                    className="d-inline-block bg-transparent tab"
                  >
                    <label className="select-orders" htmlFor="show_successful_orders">
                      Exitosas
                    </label>
                  </div>
                  <div
                    className="d-inline-block bg-transparent tab"
                  >
                    <label className="select-orders" htmlFor="show_pending_orders">
                      Pendientes
                      {totalPendindOrders > 0 && (
                        <span className="ml-5 fz-14">
                          (
                          {totalPendindOrders}
                          )
                        </span>
                      )}
                    </label>
                  </div>
                  <div
                    className="d-inline-block bg-transparent tab"
                  >
                    <label className="select-orders" htmlFor="show_cancelled_orders">
                      Canceladas
                    </label>
                  </div>
                </div>
              </div>
              <div className="col-12 px-0">
                <div>
                  <input
                    type="radio"
                    className="d-none check-toggler"
                    name="toggle-tab"
                    id="show_orders"
                    value="orders"
                    onChange={toggleTabs}
                    checked={showSection === 'orders'}
                  />
                  <input
                    type="radio"
                    className="d-none check-toggler"
                    name="toggle-tab"
                    id="show_successful_orders"
                    onChange={toggleTabs}
                    value="successful_orders"
                    checked={showSection === 'successful_orders'}
                  />
                  <input
                    type="radio"
                    className="d-none check-toggler"
                    name="toggle-tab"
                    id="show_pending_orders"
                    onChange={toggleTabs}
                    value="pending_orders"
                    checked={showSection === 'pending_orders'}
                  />
                  <input
                    type="radio"
                    className="d-none check-toggler"
                    name="toggle-tab"
                    id="show_cancelled_orders"
                    onChange={toggleTabs}
                    value="cancelled_orders"
                    checked={showSection === 'cancelled_orders'}
                  />
                  <div>
                    {!orderLoading && <OrderTableList orders={orders} noOrdersMessage="Sin órdenes de compra." />}
                    {orderLoading && (
                      <div className="order-spinner d-flex align-items-center justify-content-center">
                        <div><i className="fas fa-spinner" /></div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="t-center mt-20">
              {total_pages > 1 && (
                <ReactPaginate
                  breakLabel="..."
                  nextLabel="Siguiente"
                  onPageChange={handlePageClick}
                  pageRangeDisplayed={5}
                  marginPagesDisplayed={2}
                  pageCount={total_pages}
                  forcePage={page - 1}
                  previousLabel="Anterior"
                  renderOnZeroPageCount={null}
                  containerClassName="pagination justify-content-center"
                  pageClassName="page"
                  activeClassName="a-black"
                  previousClassName="previous"
                  nextClassName="next"
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Orders;
