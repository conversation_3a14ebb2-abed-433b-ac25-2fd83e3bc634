import React from "react";

const ProgressBarMC = ({
  progress
}) => (
  <div className="tw-flex tw-h-4 tw-overflow-hidden tw-leading-none tw-text-sm tw-bg-gray-200 tw-rounded-full">
    <div
      className="tw-h-4 tw-rounded-full tw-bg-blue-500 tw-transition-all tw-duration-300 tw-ease-in-out tw-animate-pulse"
      style={{ width: `${progress}%` }}
      role="progressbar"
      aria-valuenow={progress}
      aria-valuemin="0"
      aria-valuemax="100"
    >
      <span className="tw-text-xs tw-text-white tw-pl-2">
        {progress}
        %
      </span>
    </div>
  </div>
);

export default ProgressBarMC;
