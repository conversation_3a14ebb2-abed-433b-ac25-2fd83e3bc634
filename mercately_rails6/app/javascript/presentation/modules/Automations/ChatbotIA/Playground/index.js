/* eslint-disable no-restricted-syntax */
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Tooltip as ReactTooltip } from 'react-tooltip';

import ArrowDownIcon from 'images/chatbot_ai/arrow-down-fill.svg';
import ArrowEqualIcon from 'images/chatbot_ai/arrow-equal-fill.svg';
import ArrowUpIcon from 'images/chatbot_ai/arrow-up-fill.svg';
import { isEmpty } from "lodash";
import { getRetailerInfo } from '../../../../../actions/retailerUsersActions';
import CardButton from '../CardButton';
import AiChat from "./AiChat";
import EmptyScreen from "../EmptyScreen";
import MessagesCountModel from '../../../../models/MessageCountModel';
import customHistory from '../../../../../customHistory';
import NavbarLayoutMC from '../../../../layouts/NavbarLayoutMC';
import IconMC from "../../../../components/IconMC";

const General = () => {
  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  // MIA stats
  const [contactInteractionValue, setContactInteractionValue] = useState(0);
  const [contactInteractionPercentage, setContactInteractionPercentage] = useState('0%');
  const [contactInteractionTrend, setContactInteractionTrend] = useState('equal');

  const [messagesAnsweredValue, setMessagesAnsweredValue] = useState(0);
  const [messagesAnsweredPercentage, setMessagesAnsweredPercentage] = useState('0%');
  const [messagesAnsweredTrend, setMessagesAnsweredTrend] = useState('equal');

  const [buyIntentionValue, setBuyIntentionValue] = useState(0);
  const [buyIntentionPercentage, setBuyIntentionPercentage] = useState('0%');
  const [buyIntentionTrend, setBuyIntentionTrend] = useState('equal');

  // Date formats for MIA stats
  const currentDate = new Date();
  const currentDayNumber = currentDate.getDate();

  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
  const startDate = firstDayOfMonth.toISOString().split('T')[0];
  const endDate = currentDate.toISOString().split('T')[0];

  const lastMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
  const lastMonthName = new Intl.DateTimeFormat(t('general.stats.language'), { month: 'short' }).format(lastMonthDate);

  useEffect(() => {
    dispatch(getRetailerInfo());

    if (retailer_info.chatbot_ai) {
      loadMiaStats();
    }
  }, []);

  const loadMiaStats = () => {
    MessagesCountModel.save({ start_date: startDate, end_date: endDate })
      .then((res) => {
        processMiaStats(res);
      })
      .catch(() => {
        showtoast(t('general.stats.load_error'));
      });
  };

  const processMiaStats = (data) => {
    for (const [metric, values] of Object.entries(data)) {
      const { current_period, previous_period } = values;
      const { current, percentage, trend } = calculatePercentageChange(current_period, previous_period);

      if (metric === 'contacts_stats') {
        setContactInteractionValue(current);
        setContactInteractionPercentage(percentage);
        setContactInteractionTrend(trend);
      } else if (metric === 'messages_stats') {
        setMessagesAnsweredValue(current);
        setMessagesAnsweredPercentage(percentage);
        setMessagesAnsweredTrend(trend);
      } else if (metric === 'buy_intention_stats') {
        setBuyIntentionValue(current);
        setBuyIntentionPercentage(percentage);
        setBuyIntentionTrend(trend);
      }
    }
  };

  const calculatePercentageChange = (current, previous) => {
    previous = previous || 0;
    current = current || 0;

    let trend = 'equal';
    let percentage = '0%';

    if (previous === 0) {
      if (current > 0) {
        trend = 'increase';
        percentage = '100%';
      }
    } else {
      const change = ((current - previous) / previous) * 100;
      if (change > 0) {
        trend = 'increase';
        percentage = `${change.toFixed(2)}%`;
      } else if (change < 0) {
        trend = 'decrease';
        percentage = `${Math.abs(change).toFixed(2)}%`;
      }
    }

    return { current, percentage, trend };
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'increase':
        return ArrowUpIcon;
      case 'decrease':
        return ArrowDownIcon;
      default:
        return ArrowEqualIcon;
    }
  };

  const handleGoTo = (pathName) => {
    const urlPath = `/retailers/${ENV.SLUG}/${pathName}`;
    customHistory.push(urlPath);
  };

  const getMsnPercentage = () => `${t('general.stats.comparison_tooltip')} ${lastMonthName} - ${currentDayNumber} ${lastMonthName}`;

  return (
    <NavbarLayoutMC>
      <div className="tw-px-[30px] tw-py-[25px] tw-w-full tw-bg-white tw-overflow-y-auto tw-h-[calc(100vh-60px)]">
        {(!isEmpty(retailer_info) && !retailer_info.chatbot_ai) && (<EmptyScreen />)}
        {retailer_info.chatbot_ai && (
          <div className="tw-flex tw-flex-wrap tw-w-full">
            <div className="tw-flex-none tw-w-full tw-max-w-full">
              <h6 className="tw-font-semibold tw-text-lg tw-text-gray-700">
                {t('general.stats.title')}
              </h6>
              <div className="tw-flex tw-border tw-border-gray-200 tw-rounded-xl tw-px-6 tw-py-4 tw-gap-4 md:tw-flex-row tw-flex-col">
                <StatisticsCard
                  title={t('general.stats.messages_by_mia_title')}
                  idTooltipMsn1="infoMessages"
                  tooltipMsn1={(
                    <>
                      <span>{t('general.stats.messages_by_mia_tooltip_1')}</span>
                      <br />
                      <span>{t('general.stats.messages_by_mia_tooltip_2')}</span>
                    </>
                  )}
                  messagesAnsweredValue={messagesAnsweredValue}
                  image={getTrendIcon(messagesAnsweredTrend)}
                  messagesAnsweredTrend={messagesAnsweredTrend}
                  messagesAnsweredPercentage={messagesAnsweredPercentage}
                  idTooltipMsn2="dateMessages"
                  tooltipMsn2={getMsnPercentage()}
                />

                <div className="tw-inline-block"><div className="tw-w-[1px] tw-h-full tw-bg-gray-200 tw-border-none tw-my-0"></div></div>

                <StatisticsCard
                  title={t('general.stats.contact_interaction_title')}
                  idTooltipMsn1="infoContacts"
                  tooltipMsn1={(
                    <>
                      <span>{t('general.stats.contact_interaction_tooltip_1')}</span>
                      <br />
                      <span>{t('general.stats.contact_interaction_tooltip_2')}</span>
                    </>
                  )}
                  messagesAnsweredValue={contactInteractionValue}
                  image={getTrendIcon(contactInteractionTrend)}
                  messagesAnsweredTrend={contactInteractionTrend}
                  messagesAnsweredPercentage={contactInteractionPercentage}
                  idTooltipMsn2="dateContacts"
                  tooltipMsn2={getMsnPercentage()}
                />

                <div className="tw-inline-block"><div className="tw-w-[1px] tw-h-full tw-bg-gray-200 tw-border-none tw-my-0"></div></div>

                <StatisticsCard
                  title={t('general.stats.buy_intention_title')}
                  idTooltipMsn1="infoBuyIntention"
                  tooltipMsn1={t('general.stats.buy_intention_tooltip_1')}
                  messagesAnsweredValue={buyIntentionValue}
                  image={getTrendIcon(buyIntentionTrend)}
                  messagesAnsweredTrend={buyIntentionTrend}
                  messagesAnsweredPercentage={buyIntentionPercentage}
                  idTooltipMsn2="dateBuyIntention"
                  tooltipMsn2={getMsnPercentage()}
                />
              </div>
            </div>

            <div className="tw-flex tw-flex-wrap tw-mt-5 tw-gap-6">
              <div className="tw-w-[641px]">
                <AiChat currentRetailer={retailer_info} />
              </div>
              <div className="tw-flex tw-items-center tw-justify-center tw-w-[450px]">
                <div className="">
                  <h2 className="tw-font-semibold tw-text-[40px] tw-text-gray-950 tw-mb-4">
                    {t('general.title')}
                  </h2>
                  <div className="tw-font-regular tw-text-sm tw-text-gray-950 tw-break-words tw-whitespace-normal">
                    {t('general.subtitle')}
                  </div>
                  <h6 className="tw-font-medium tw-text-base text-gray-950 tw-mt-[42px]">
                    {t('general.buttons_header')}
                  </h6>
                  <div className="tw-mt-4 tw-cursor-pointer tw-min-w-[400px]" onClick={() => { handleGoTo('chatbot_ai/guides'); }}>
                    <CardButton
                      icon="new-document"
                      title={t('general.train.title')}
                      description={t('general.train.description')}
                      isIcon
                    />
                  </div>
                  <div className="tw-mt-4 tw-cursor-pointer tw-min-w-[400px]" onClick={() => { handleGoTo('chatbot_ai/config'); }}>
                    <CardButton
                      icon="settings-outline"
                      title={t('general.config.title')}
                      description={t('general.config.description')}
                      isIcon
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </NavbarLayoutMC>
  );
};

const StatisticsCard = ({
  title,
  idTooltipMsn1,
  tooltipMsn1,
  messagesAnsweredValue,
  image,
  messagesAnsweredTrend,
  messagesAnsweredPercentage,
  idTooltipMsn2,
  tooltipMsn2
}) => (
  <div className="tw-flex tw-flex-col">
    <div className="tw-flex tw-items-center">
      <span className="tw-text-xs tw-text-gray-950">{title}</span>
      <span data-tooltip-id={idTooltipMsn1}>
        <IconMC name="info-outline" size="sm" className="tw-ml-1 tw-text-sm tw-text-gray-700" />
      </span>
      <ReactTooltip id={idTooltipMsn1} place="top" backgroundColor="#3C4348" className="tw-text-left tw-rounded-xl">
        {tooltipMsn1}
      </ReactTooltip>
    </div>
    <div className="tw-flex">
      <span className="tw-font-regular tw-text-lg tw-text-gray-950">{messagesAnsweredValue}</span>
      <span className="tw-ml-1 tw-text-xs tw-mt-2">
        <img src={image} />
      </span>
      <span className={`tw-text-2xs tw-mt-2 ${messagesAnsweredTrend === 'equal' ? 'tw-text-red-400' : 'tw-text-green-400'}`} data-tooltip-id={idTooltipMsn2}>{messagesAnsweredPercentage}</span>
      <ReactTooltip id={idTooltipMsn2} place="top" className="tw-bg-white tw-rounded-xl tw-text-gray-950 tw-border tw-border-[#E4E4E4] tw-text-sm">
        {tooltipMsn2}
      </ReactTooltip>
    </div>
  </div>
);

export default General;
