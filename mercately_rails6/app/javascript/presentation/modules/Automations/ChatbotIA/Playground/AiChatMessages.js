/* eslint-disable no-nested-ternary */
/* eslint-disable import/no-unresolved */
import React, { useEffect, useRef, useState } from 'react';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { useTranslation } from 'react-i18next';

import BuyMessageIcon from 'images/chatbot_ai/buy_message.svg';
import RaisedHand from 'images/chatbot_ai/raise_hand.svg';
import pdfWs from 'images/pdf-ws.png';
import { classByMessageDirection } from '../../../../../util/messageUtil';
import MessageStatus from '../../../../../components/shared/MessageStatus';
import MiaInternalMessageMC from './MiaInternalMessageMC';
import { uuid } from '../../../../../util/utils';

import SourcesModal from './SourcesModal';
import IconMC from '../../../../components/IconMC';

const PdfComponent = ({ url, fileName }) => {
  const getFileNameFromUrl = (_url) => {
    try {
      const filename = _url.split('/').pop();
      return decodeURIComponent(filename);
    } catch (error) {
      return url.split('/').pop();
    }
  };

  const displayName = fileName || getFileNameFromUrl(url);

  return (
    <a href={url} className="text-blue" target="blank">
      <img src={pdfWs} title="pdf" className="pdf-icon" width="20px" />
      <span className="ml-5">{displayName}</span>
    </a>
  );
}


const ImageComponent = ({ url }) => (
  <div className="tw-relative tw-border-0 tw-rounded-t-[14px] tw-bg-white tw-h-auto">
    <img src={url} className="tw-object-cover tw-w-full tw-h-full tw-rounded-[9px] tw-align-middle tw-border-none" alt="chat" />
  </div>
);

const FormattedText = ({ text }) => {
  const lines = text.split('\n');

  const elements = [];
  let listItems = [];
  let currentListType = null; // "ul" o "ol"

  lines.forEach((line, i) => {
    const trimmed = line.trim();
    const isBulletItem = /^[-*]\s+/.test(trimmed);
    const isNumberedItem = /^\d+\.\s+/.test(trimmed);

    if (isBulletItem || isNumberedItem) {
      const listType = isNumberedItem ? 'ol' : 'ul';
      const className = isNumberedItem ? 'ordered-list' : 'unordered-list';
      const content = trimmed.replace(isNumberedItem ? /^\d+\.\s+/ : /^[-*]\s+/, '');

      // Si cambia el tipo de lista, cerramos la actual
      if (currentListType && currentListType !== listType) {
        elements.push(
          React.createElement(
            currentListType,
            { key: `list-${i}`, className: currentListType === 'ul' ? 'unordered-list' : 'ordered-list' },
            listItems
          )
        );
        listItems = [];
      }

      currentListType = listType;

      listItems.push(
        <li key={`li-${i}`}>{formatInlineText(content)}</li>
      );
    } else {
      // Si hay una lista abierta, ciérrala
      if (listItems.length > 0) {
        elements.push(
          React.createElement(
            currentListType,
            { key: `list-${i}` },
            listItems
          )
        );
        listItems = [];
        currentListType = null;
      }

      if (trimmed !== '') {
        elements.push(
          <p key={`p-${i}`}>{formatInlineText(trimmed)}</p>
        );
      }
    }
  });

  // Cerrar lista si quedó abierta
  if (listItems.length > 0) {
    elements.push(
      React.createElement(
        currentListType,
        { key: `list-end` },
        listItems
      )
    );
  }

  return <div>{elements}</div>;
}

const formatInlineText = (text) => {
  const parts = text.split(/(\*[^*]+\*|_[^_]+_)/g);

  return parts.map((part, index) => {
    if (/^\*[^*]+\*$/.test(part)) {
      return <strong key={index}>{part.slice(1, -1)}</strong>;
    } else if (/^_[^_]+_$/.test(part)) {
      return <em key={index}>{part.slice(1, -1)}</em>;
    } else {
      return <span key={index}>{part}</span>;
    }
  });
}

const AiChatMessages = React.memo(({ messages, scrollContainerRef }) => {
  const chatEndRef = useRef(null);
  const { t } = useTranslation('ChatbotIA');

  const [openSourcesModal, setOpenSourcesModal] = useState(false);
  const [sources, setSources] = useState(null);

  const scrollToBottom = () => {
    if (scrollContainerRef?.current) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getAudioType = (audioUrl) => {
    const extension = audioUrl.split('.').pop();
    switch (extension) {
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      default:
        return 'audio/mpeg';
    }
  };

  const internalMessageIcon = (message) => {
    switch (message.flag) {
      case 'buy':
        return BuyMessageIcon;
      case 'help':
        return RaisedHand;
      default:
        return null;
    }
  };

  const internalMessageTextColor = (message) => {
    switch (message.flag) {
      case 'buy':
        return "#8D7EA7";
      case 'help':
        return "#9F6AFC";
      default:
        return null;
    }
  };

  const internalMessageMessageKey = (message) => {
    switch (message.flag) {
      case 'buy':
        return `${t('config.buy_intention.message')}`;
      case 'help':
        return `${t('config.human_help_message')}`;
      default:
        return null;
    }
  };

  const handleOpenSourcesModal = (id) => {
    const message = messages.find((msg) => msg.message_identifier === id);

    setOpenSourcesModal(true);
    setSources(message.sources);
  };

  const handleCloseSourcesModal = () => {
    setOpenSourcesModal(false);
    setSources(null);
  };

  return (
    <div className="tw-p-6">
      {messages.map((message) => (
        <div key={message.message_identifier}>
          <div
            className={`message tw-relative tw-overflow-hidden tw-py-2 tw-px-1 tw-text-gray-950 tw-break-words ${message.direction === 'outbound' && 'tw-flex tw-flex-row-reverse'}`}
          >
            <div
              className={`${classByMessageDirection(message)} tw-shadow-none tw-min-w-[150px] ${message.direction === 'outbound' ? 'tw-bg-[#D9FDD3]' : 'tw-bg-white'}`}
            >
              <div className="tw-text-xs message-content">
                {message.content_file ? (
                  <div className="tw-w-[300px] tw-bg-white tw-rounded-[14px]">
                    <div className="tw-relative tw-border-0 tw-rounded-t-[14px] tw-bg-white tw-h-auto">
                      {message.content_file_type?.includes('pdf') ? (
                        <PdfComponent url={message.content_file} fileName={message.content_file_name} />
                      ) : (
                        <ImageComponent url={message.content_file} />
                      )}
                    </div>
                    {message.content_text && (
                      <FormattedText
                        className="tw-bg-white tw-px-3 tw-rounded-b-[14px] tw-text-xs tw-leading-[1.4]"
                        style={message.direction === 'inbound' ? { backgroundColor: "#FFFFFF" } : { backgroundColor: "#D9FDD3" }}
                        text={message.content_text}
                      />
                    )}
                  </div>
                ) : message.content_audio ? (
                  <audio controls>
                    <source src={message.content_audio} type={getAudioType(message.content_audio)} />
                    Your browser does not support the audio element.
                  </audio>
                ) : typeof message.content_text === 'object' ? (
                  <div className="tw-w-[300px]">
                    <div className="tw-relative tw-border-0 tw-rounded-t-[14px] tw-bg-white tw-h-auto">
                      <ImageComponent url={message.content_text.url} />
                    </div>
                    {message.content_text.text && (
                      <FormattedText
                        className="tw-bg-white tw-px-3 tw-rounded-b-[14px] tw-text-xs tw-leading-[1.4]"
                        text={message.content_text.text}
                      />
                    )}
                  </div>
                ) : (
                  <FormattedText text={message.content_text} />
                )}
              </div>

              <div className="tw-text-right">
                <div className="tw-inline tw-text-3xs tw-text-gray-500">
                  <MessageStatus
                    dateFormat="DD-MM-YYYY HH:mm"
                    chatType="whatsapp"
                    message={message}
                    handleMessageEvents
                    containerClassName="!tw-flex !tw-justify-end !tw-items-center"
                  />
                </div>
              </div>
            </div>

            {message.direction === 'inbound' && message.sources && (
              <div className="tw-w-4 tw-cursor-pointer" onClick={() => handleOpenSourcesModal(message.id)} data-tooltip-id="source-message">
                <IconMC
                  name="mia-request"
                  size="xl"
                />
              </div>
            )}
          </div>
          {['buy', 'help'].includes(message.flag) && (
            <div key={message.message_identifier}>
              <div key={uuid()} className="message tw-relative tw-overflow-hidden tw-py-2 tw-px-1 tw-text-gray-950 tw-break-words tw-flex tw-items-center">
                <div className="tw-text-white tw-break-words tw-relative tw-inline-block tw-align-top tw-max-w-[75%] tw-rounded-xl tw-text-sm tw-font-normal tw-m-0 tw-p-3 tw-shadow-[0_1px_4px_0_rgba(209,209,209,1)] mia-message tw-bg-white">
                  <div className="tw-relative tw-w-full">
                    <MiaInternalMessageMC
                      icon={internalMessageIcon(message)}
                      textColor={internalMessageTextColor(message)}
                      messageKey={internalMessageMessageKey(message)}
                      internalMessageKey="config.internal_message"
                      chatType="whatsapp"
                      message={{ ...message, direction: 'outbound' }}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      ))}

      <div ref={chatEndRef} />

      {openSourcesModal && sources && (
        <SourcesModal sources={sources} onClose={handleCloseSourcesModal} />
      )}

      {!openSourcesModal && (
        <ReactTooltip id="source-message" place="top" updateOnDomChange>
          <span>{t('general.source_tooltip')}</span>
        </ReactTooltip>
      )}
    </div>
  );
});

export default AiChatMessages;
