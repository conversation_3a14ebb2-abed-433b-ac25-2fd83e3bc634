/* eslint-disable import/no-unresolved */
import React from 'react';
import { useTranslation } from 'react-i18next';
import ChatbotImage from 'images/chatbot_ai/mercately_ai_empty_screen.svg';
import ButtonComponent from '../../../components/ButtonMC';

const EmptyScreen = () => {
  const { t } = useTranslation('ChatbotIA');

  return (
    <div className="tw-wrapper">
      <div className="tw-w-full sm:tw-w-[540px] md:tw-w-[720px] lg:tw-w-[960px] xl:tw-w-[1140px] 2xl:tw-w-[1320px] tw-mx-auto tw-tw-px-[30px]">
        <div className="tw-flex tw-flex-wrap tw-mt-[64px]">
          <div className="tw-text-left tw-w-full md:tw-w-full lg:tw-w-8/12">

            <div className="tw-bg-orange-50 tw-p-[25px] tw-mb-5">
              {t('emptyScreen.text1')}
              <br />
              {' '}
              <br />
              {t('emptyScreen.text2')}
            </div>

            <h1 className="tw-font-semibold tw-text-4xl tw-text-gray-950">
              {t('emptyScreen.title')}
            </h1>
            <p className="tw-text-gray-500 tw-mt-8 tw-mb-[42px] tw-text-sm">
              {t('emptyScreen.text3')}
            </p>

            <ButtonComponent
              text={t('emptyScreen.textBtn')}
              onClick={() => window.open('https://forms.gle/FbPyjHP1Nx2i1Tw38', '_blank')}
            />
          </div>

          <div className="tw-w-full md:tw-w-full lg:tw-w-4/12 tw-text-center">
            <img src={ChatbotImage} alt="Chatbot AI" className="tw-max-w-full tw-h-auto lg:tw-mt-0 md:tw-mt-[5px]" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmptyScreen;
