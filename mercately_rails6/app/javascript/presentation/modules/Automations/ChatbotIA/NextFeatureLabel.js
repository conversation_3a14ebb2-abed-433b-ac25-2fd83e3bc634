import React from 'react';
import { useTranslation } from 'react-i18next';

const NextFeatureLabel = () => {
  const { t } = useTranslation('ChatbotIA');

  return (
    <span className="tw-font-regular tw-text-blue-950 tw-text-xs tw-leading-4 tw-bg-yellow-200 tw-rounded-full tw-px-2 tw-py-[2px] tw-font-medium tw-text-2xs">
      {t('config.coming_soon')}
    </span>
  );
};

export default NextFeatureLabel;
