import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

import ModalSection from '../ModalSection';
import SectionModel from '../../../../models/SectionModel';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import QuillComponent from '../QuillComponent';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const Restrictions = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const [restrictions, setRestrictions] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadRestrictionsContent();
  }, []);

  const toggleRestrictionsModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const loadRestrictionsContent = () => {
    SectionModel.getByType('restrictions')
      .then((res) => {
        setRestrictions(res.restrictions || '');
        setSaving(false);
      })
      .catch((err) => {
        setSaving(false);
        showtoast(err.error || t('config.error_loading'));
      });
  };

  const updateInfo = (value) => {
    setRestrictions(value);
  };

  const onSubmit = () => {
    const params = { type: 'restrictions', restrictions };
    setSaving(true);

    SectionModel.update(params, csrfToken)
      .then(() => {
        setTimeout(() => {
          showtoast(t('config.save_success'));
          setSaving(false);
          toggleRestrictionsModal();
        }, 1000);
      })
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
        toggleRestrictionsModal();
      });
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.restrictions.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <ModalSection
          title={t('config.restrictions.section_1_title')}
          description={t('config.restrictions.section_1_description')}
          afterContent={(
            <QuillComponent
              name="restrictions"
              value={restrictions || ''}
              onChange={(value) => { updateInfo(value); }}
            />
          )}
        />
      </RightModalLayout>
    </div>
  );
};

export default Restrictions;
