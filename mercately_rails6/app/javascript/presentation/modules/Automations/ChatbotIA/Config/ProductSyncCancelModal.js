import React from 'react';
import { useTranslation } from 'react-i18next';

import ButtonMC from '../../../../components/ButtonMC';
import ModalMC from '../../../../components/ModalMC';
import LoadingMC from '../../../../components/LoadingMC';

const ProductSyncCancelModal = ({
  isOpen, onClose, onConfirm, loading
}) => {
  const { t } = useTranslation('ChatbotIA');

  return (
    <ModalMC
      visible={isOpen}
      onClose={onClose}
      containerClassName="!tw-max-w-2xl"
      className="tw-px-8 tw-pb-14 tw-pt-8"
      overlayClassName="tw-z-[99999]"
    >
      <div>
        <div className="tw-font-bold tw-text-gray-950 tw-flex tw-items-center tw-justify-between tw-text-lg">
          {t('config.products.disable_sync_title')}
        </div>

        <p className="tw-text-sm tw-mb-[25px]">
          {t('config.products.disable_sync_description')}
        </p>

        <div className="tw-flex tw-justify-end tw-gap-2">
          <ButtonMC
            text={t('config.products.close')}
            onClick={onClose}
            className="tw-text-sm"
            variant="SECONDARY"
            outlined
          />
          <ButtonMC
            text={t('config.products.confirm')}
            onClick={onConfirm}
            className="tw-text-sm"
            disabled={loading}
            icon={loading ? <LoadingMC loading={loading} /> : null}
          />
        </div>
      </div>
    </ModalMC>
  );
};

export default ProductSyncCancelModal;
