import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

import ModalSection from '../ModalSection';
import SectionModel from '../../../../models/SectionModel';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import QuillComponent from '../QuillComponent';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const StepByStepModal = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const [stepByStep, setStepByStep] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadStepByStepContent();
  }, []);

  const toggleStepByStepModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const loadStepByStepContent = () => {
    SectionModel.getByType('conversation_flow')
      .then((res) => {
        setStepByStep(res.conversation_flow || '');
        setSaving(false);
      })
      .catch((err) => {
        setSaving(false);
        showtoast(err.error || t('config.error_loading'));
      });
  };

  const updateInfo = (value) => {
    setStepByStep(value);
  };

  const onSubmit = () => {
    const params = { type: 'conversation_flow', conversation_flow: stepByStep };
    setSaving(true);

    SectionModel.update(params, csrfToken)
      .then(() => {
        setTimeout(() => {
          showtoast(t('config.save_success'));
          setSaving(false);
          toggleStepByStepModal();
        }, 1000);
      })
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
        toggleStepByStepModal();
      });
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.step_by_step.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <ModalSection
          title={t('config.step_by_step.section_1_title')}
          description={t('config.step_by_step.section_1_description')}
          afterContent={(
            <QuillComponent
              name="step_by_step"
              value={stepByStep || ''}
              onChange={(value) => { updateInfo(value); }}
            />
          )}
        />
      </RightModalLayout>
    </div>
  );
};

export default StepByStepModal;
