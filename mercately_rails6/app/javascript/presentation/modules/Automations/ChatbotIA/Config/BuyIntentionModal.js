import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import ModalSection from '../ModalSection';

import SectionModel from '../../../../models/SectionModel';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import QuillComponent from '../QuillComponent';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const BuyIntentionModal = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const [content, setContent] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadBuyIntention();
  }, []);

  const toggleBuyIntentionModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const loadBuyIntention = () => {
    SectionModel.getByType('buy_intention')
      .then((res) => {
        setContent(res.buy_intention || '');
      })
      .catch((err) => {
        showtoast(err.error || t('config.error_loading'));
      });
  };

  const updateInfo = (value) => {
    setContent(value);
  };

  const onSubmit = () => {
    const params = {
      type: 'buy_intention',
      buy_intention: content
    };

    setSaving(true);

    SectionModel.update(params, csrfToken)
      .then(() => {
        setTimeout(() => {
          showtoast(t('config.save_success'));
          setSaving(false);
          toggleBuyIntentionModal();
        }, 1000);
      })
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
        toggleBuyIntentionModal();
      });
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.buy_intention.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <ModalSection
          title={t('config.buy_intention.section_1_title')}
          description={t('config.buy_intention.section_1_description')}
          afterContent={(
            <QuillComponent
              name="buy_intention"
              value={content || ''}
              onChange={(value) => { updateInfo(value); }}
              placeholder={t('config.buy_intention.placeholder')}
            />
          )}
        />
      </RightModalLayout>
    </div>
  );
};

export default BuyIntentionModal;
