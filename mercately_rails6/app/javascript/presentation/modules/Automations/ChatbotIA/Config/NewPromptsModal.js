import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';
import newFeatureImage from 'images/chatbot_ai/new_feature.svg';

import FeatureFlagModel from '../../../../models/FeatureFlagModelMC';
import ModalMC from '../../../../components/ModalMC';
import ButtonComponent from '../../../../components/ButtonMC';
import CustomIcon from '../../../../components/CustomIconMC';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const NewPromptsModal = forwardRef((_, modalRef) => {
  const { t } = useTranslation('ChatbotIA');
  const [isOpen, setIsOpen] = useState(false);
  const [saving, setSaving] = useState(false);

  const toggleModal = () => {
    setIsOpen(!isOpen);
  };

  useImperativeHandle(modalRef, () => ({
    toggleModal
  }));

  const onSubmit = () => {
    if (!window.confirm(t('config.prompts_modal.confirm_update'))) {
      return;
    }

    if (saving) return;

    setSaving(true);

    FeatureFlagModel.update({}, csrfToken)
      .then(() => {
        showtoast(t('config.save_success'));
        setSaving(false);
        toggleModal();
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      })
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
      });
  };

  return (
    <ModalMC
      visible={isOpen}
      onClose={toggleModal}
      containerClassName="!tw-mx-auto !tw-w-fit !tw-max-w-full tw-flex tw-justify-center"
      className="tw-overflow-y-auto tw-w-[80%]"
    >
      <div className="tw-flex tw-justify-around tw-px-[30px] tw-flex-row">
        <div className="tw-flex tw-flex-col tw-pr-[35px] tw-w-[40%]">
          <h className="tw-text-xl tw-font-bold tw-leading-[32px]">
            {t('config.prompts_modal.title')}
          </h>

          <div className="tw-border-l-[4px] tw-border-blue-300 tw-pl-[15px] tw-mt-5 tw-mb-[25px]">
            <h className="tw-text-sm tw-font-500 tw-leading-6">
              {t('config.prompts_modal.subtitle_1')}
            </h>
            <p className="tw-text-xs tw-font-400 tw-leading-4 tw-mb-0">
              {t('config.prompts_modal.description_1')}
            </p>
          </div>

          <div className="tw-border-l-[4px] tw-border-blue-300 tw-pl-[15px] tw-mt-5 tw-mb-[25px]">
            <h className="tw-font-500 tw-text-sm tw-leading-6">
              {t('config.prompts_modal.subtitle_2')}
            </h>
            <p className="tw-font-400 tw-text-xs tw-leading-4 tw-mb-0">
              {t('config.prompts_modal.description_2')}
            </p>
          </div>

          <div className="tw-mb-[25px]">
            <div className="tw-flex tw-items-center tw-rounded-xl tw-p-[15px] tw-mb-[25px] tw-border tw-border-blue-200 tw-bg-blue-100">
              <CustomIcon name="info-outline" className="tw-text-blue-500" />
              <span className="tw-font-regular tw-text-blue-500 tw-text-xs tw-ml-1">
                {t('config.prompts_modal.info_msg')}
              </span>
            </div>

            <div className="tw-flex tw-items-center tw-rounded-xl tw-p-[15px] tw-mb-[25px] tw-border tw-border-yellow-200 tw-bg-yellow-100">
              <CustomIcon name="info-outline" className="tw-text-yellow-500" />
              <span className="tw-font-regular tw-text-yellow-500 tw-text-xs tw-ml-1">
                {t('config.prompts_modal.warning_msg')}
              </span>
            </div>
          </div>

          <div className="tw-flex tw-justify-around tw-mb-2.5">
            <ButtonComponent
              text={t('config.prompts_modal.update')}
              onClick={onSubmit}
              className="tw-text-sm tw-w-[40%]"
            />

            <ButtonComponent
              text={t('config.prompts_modal.cancel')}
              onClick={toggleModal}
              className="tw-text-sm tw-w-[40%]"
              variant="SECONDARY"
              outlined
            />
          </div>
        </div>

        <div className="tw-content-center tw-justify-center">
          <img
            src={newFeatureImage}
            className="tw-max-w-full tw-h-auto"
          />
        </div>
      </div>
    </ModalMC>
  );
});

export default NewPromptsModal;
