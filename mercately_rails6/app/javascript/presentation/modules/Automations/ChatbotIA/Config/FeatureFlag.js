import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import NewPromptsModal from './NewPromptsModal';
import ButtonMC from '../../../../components/ButtonMC';
import ButtonIconMC from '../../../../components/ButtonIconMC';

const FeatureFlag = () => {
  const { t } = useTranslation('ChatbotIA');
  const [isVisible, setIsVisible] = useState(true);
  const modalRef = useRef(null);

  const handleClose = () => {
    setIsVisible(false);
  };

  const handleActivate = () => {
    if (modalRef.current) {
      modalRef.current.toggleModal();
    }
  };

  if (!isVisible) return null;

  return (
    <>
      <div className="tw-flex tw-justify-between tw-items-center tw-bg-blue-950 tw-px-4 tw-py-2 -tw-ml-[30px] -tw-mt-5 tw-mb-[15px] md:tw-max-w-[1026px]:-tw-translate-y-3">
        <div className="tw-flex tw-items-center tw-gap-5">
          <div className="tw-font-semibold tw-text-white">
            {t('config.feature_flag.message')}
          </div>
          <div className="tw-px-[15px] tw-mr-[15px] tw-rounded-2 tw-font-medium tw-no-underline tw-cursor-pointer">
            <ButtonMC
              text={t('config.feature_flag.button')}
              onClick={handleActivate}
              className="!tw-bg-yellow-300 tw-font-medium !tw-text-blue-950"
            />
          </div>
        </div>

        <ButtonIconMC
          icon="close-outline"
          iconClassName="tw-text-white"
          onClick={handleClose}
        />
      </div>

      <NewPromptsModal ref={modalRef} />
    </>
  );
};

export default FeatureFlag;
