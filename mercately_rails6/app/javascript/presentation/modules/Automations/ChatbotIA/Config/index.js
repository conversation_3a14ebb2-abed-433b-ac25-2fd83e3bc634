/* eslint-disable import/no-unresolved */
import React, { useEffect } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { isEmpty } from "lodash";

import PlatformsIcon from 'images/chatbot_ai/platforms.svg';
import InstructionsIcon from 'images/chatbot_ai/instructions.svg';
import GoalIcon from 'images/chatbot_ai/goal.svg';
import GeneralParametersIcon from 'images/chatbot_ai/general_parameters.svg';
import StepByStepIcon from 'images/chatbot_ai/step_by_step.svg';
import InteractionExamplesIcon from 'images/chatbot_ai/interaction_examples.svg';
import RestrictionsIcon from 'images/chatbot_ai/restrictions.svg';
import HandoffIcon from 'images/chatbot_ai/handoff.svg';
import FollowUpIcon from 'images/chatbot_ai/follow_up.svg';
import ClientDataIcon from 'images/chatbot_ai/client_data.svg';
import NegotiationsIcon from 'images/chatbot_ai/negotiations.svg';
import ProductIcon from 'images/chatbot_ai/product.svg';
import BuyIntentionIcon from 'images/chatbot_ai/buy_intention.svg';
import FeatureFlag from "./FeatureFlag";
import EmptyScreen from "../EmptyScreen";
import CardButton from "../CardButton";

import { getRetailerInfo } from '../../../../../actions/retailerUsersActions';
import NavbarLayoutMC from "../../../../layouts/NavbarLayoutMC";
import { SIDEBAR_ACTIONS } from "../../../../../constants/actionsConstants";

const Config = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);

  const showNewPrompts = retailer_info?.feature_flag;

  useEffect(() => {
    dispatch(getRetailerInfo());
  }, [dispatch]);

  const openModal = (component) => {
    dispatch({
      type: SIDEBAR_ACTIONS.OPEN_SIDEBAR,
      payload: {
        componentKey: component,
        props: {}
      }
    });
  };

  return (
    <NavbarLayoutMC>
      <div className="tw-px-[30px] tw-py-[20px] tw-w-full tw-bg-white tw-overflow-y-auto tw-h-[calc(100vh-60px)]">
        {(!isEmpty(retailer_info) && !retailer_info.chatbot_ai) && (<EmptyScreen />)}
        {retailer_info.chatbot_ai && (
          <>
            {!showNewPrompts && (<FeatureFlag />)}

            <div className="tw-flex">
              <span className="tw-font-semibold tw-text-lg tw-text-gray-700">{t('config.header_title')}</span>
            </div>

            <div className="tw-w-full sm:tw-w-7/12 md:tw-w-7/12 tw-flex tw-flex-col tw-px-0">
              <div className="tw-mt-5" onClick={() => openModal('PlatformsModal')}>
                <CardButton
                  icon={PlatformsIcon}
                  title={t('config.platforms.title')}
                  description={t('config.platforms.description')}
                />
              </div>

              <div className="tw-w-full sm:tw-w-full tw-px-0 tw-mt-5">
                <span className="tw-font-semibold tw-text-sm tw-text-gray-950">{t('config.general_instructions')}</span>
              </div>

              {!showNewPrompts && (
                <div className="tw-mt-5" onClick={() => openModal('InstructionsModal')}>
                  <CardButton
                    icon={InstructionsIcon}
                    title={t('config.instructions.title')}
                    description={t('config.instructions.description')}
                  />
                </div>
              )}

              {showNewPrompts && (
                <>
                  <div className="tw-mt-5" onClick={() => openModal('GoalModal')}>
                    <CardButton
                      icon={GoalIcon}
                      title={t('config.goal.title')}
                      description={t('config.goal.description')}
                    />
                  </div>

                  <div className="tw-mt-5" onClick={() => openModal('GeneralParametersModal')}>
                    <CardButton
                      icon={GeneralParametersIcon}
                      title={t('config.general_parameters.title')}
                      description={t('config.general_parameters.description')}
                    />
                  </div>

                  <div className="tw-mt-5" onClick={() => openModal('StepByStepModal')}>
                    <CardButton
                      icon={StepByStepIcon}
                      title={t('config.step_by_step.title')}
                      description={t('config.step_by_step.description')}
                    />
                  </div>

                  <div className="tw-mt-5" onClick={() => openModal('InteractionExamplesModal')}>
                    <CardButton
                      icon={InteractionExamplesIcon}
                      title={t('config.interaction_examples.title')}
                      description={t('config.interaction_examples.description')}
                    />
                  </div>

                  <div className="tw-mt-5" onClick={() => openModal('RestrictionsModal')}>
                    <CardButton
                      icon={RestrictionsIcon}
                      title={t('config.restrictions.title')}
                      description={t('config.restrictions.description')}
                    />
                  </div>
                </>
              )}

              <div className="tw-mt-5" onClick={() => openModal('HandoffModal')}>
                <CardButton
                  icon={HandoffIcon}
                  title={t('config.handoff.title')}
                  description={t('config.handoff.description')}
                />
              </div>

              {showNewPrompts && (
                <>
                  <div className="tw-w-full sm:tw-w-full tw-px-0 tw-mt-5">
                    <span className="tw-font-semibold tw-text-sm tw-text-gray-950">{t('config.clients_instructions')}</span>
                  </div>

                  <div className="tw-mt-5">
                    <CardButton
                      icon={FollowUpIcon}
                      title={t('config.follow_up.title')}
                      description={t('config.follow_up.description')}
                      futureFeature
                    />
                  </div>

                  <div className="tw-mt-5">
                    <CardButton
                      icon={ClientDataIcon}
                      title={t('config.client_data.title')}
                      description={t('config.client_data.description')}
                      futureFeature
                    />
                  </div>

                  <div className="tw-mt-5">
                    <CardButton
                      icon={NegotiationsIcon}
                      title={t('config.negotiations.title')}
                      description={t('config.negotiations.description')}
                      futureFeature
                    />
                  </div>
                </>
              )}

              <div className="tw-w-full sm:tw-w-full tw-px-0 tw-mt-5">
                <span className="tw-font-semibold tw-text-sm tw-text-gray-950">{t('config.products_instructions')}</span>
              </div>

              <div className="tw-mt-5" onClick={() => openModal('ProductModal')}>
                <CardButton
                  icon={ProductIcon}
                  title={t('config.products.title')}
                  description={t('config.products.description')}
                />
              </div>

              <div className="tw-mt-5" onClick={() => openModal('BuyIntentionModal')}>
                <CardButton
                  icon={BuyIntentionIcon}
                  title={t('config.buy_intention.title')}
                  description={t('config.buy_intention.description')}
                />
              </div>
            </div>
          </>
        )}
      </div>
    </NavbarLayoutMC>
  );
};

export default Config;
