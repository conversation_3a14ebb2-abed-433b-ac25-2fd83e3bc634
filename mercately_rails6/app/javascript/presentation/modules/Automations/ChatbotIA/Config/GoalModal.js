import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

import ModalSection from '../ModalSection';
import SectionModel from '../../../../models/SectionModel';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import QuillComponent from '../QuillComponent';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const GoalModal = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const [roleAndGoal, setRoleAndGoal] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadGoalContent();
  }, []);

  const toggleGoalModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const loadGoalContent = () => {
    SectionModel.getByType('role_and_goal')
      .then((res) => {
        setRoleAndGoal(res.role_and_goal || '');
        setSaving(false);
      })
      .catch((err) => {
        setSaving(false);
        showtoast(err.error || t('config.error_loading'));
      });
  };

  const updateInfo = (value) => {
    setRoleAndGoal(value);
  };

  const onSubmit = () => {
    const params = { type: 'role_and_goal', role_and_goal: roleAndGoal };
    setSaving(true);

    SectionModel.update(params, csrfToken)
      .then(() => {
        setTimeout(() => {
          showtoast(t('config.save_success'));
          setSaving(false);
          toggleGoalModal();
        }, 1000);
      })
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
        toggleGoalModal();
      });
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.goal.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <ModalSection
          title={t('config.goal.section_1_title')}
          description={t('config.goal.section_1_description')}
          afterContent={(
            <QuillComponent
              name="role_and_goal"
              value={roleAndGoal || ''}
              onChange={(value) => { updateInfo(value); }}
            />
          )}
        />
      </RightModalLayout>
    </div>
  );
};

export default GoalModal;
