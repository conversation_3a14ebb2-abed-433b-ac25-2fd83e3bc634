import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

import ModalSection from '../ModalSection';
import SectionModel from '../../../../models/SectionModel';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import QuillComponent from '../QuillComponent';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const InteractionExamplesModal = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const [examplesInteraction, setExamplesInteraction] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadExamplesInteractionContent();
  }, []);

  const toggleInteractionExamplesModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const loadExamplesInteractionContent = () => {
    SectionModel.getByType('examples_interaction')
      .then((res) => {
        setExamplesInteraction(res.examples_interaction || '');
        setSaving(false);
      })
      .catch((err) => {
        setSaving(false);
        showtoast(err.error || t('config.error_loading'));
      });
  };

  const updateInfo = (value) => {
    setExamplesInteraction(value);
  };

  const onSubmit = () => {
    const params = { type: 'examples_interaction', examples_interaction: examplesInteraction };
    setSaving(true);

    SectionModel.update(params, csrfToken)
      .then(() => {
        setTimeout(() => {
          showtoast(t('config.save_success'));
          setSaving(false);
          toggleInteractionExamplesModal();
        }, 1000);
      })
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
        toggleInteractionExamplesModal();
      });
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.interaction_examples.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <ModalSection
          title={t('config.interaction_examples.section_1_title')}
          description={t('config.interaction_examples.section_1_description')}
          afterContent={(
            <QuillComponent
              name="examples_interaction"
              value={examplesInteraction || ''}
              onChange={(value) => { updateInfo(value); }}
            />
          )}
        />
      </RightModalLayout>
    </div>
  );
};

export default InteractionExamplesModal;
