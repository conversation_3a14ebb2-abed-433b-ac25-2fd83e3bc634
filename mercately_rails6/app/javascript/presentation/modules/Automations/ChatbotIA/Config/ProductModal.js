/* eslint-disable consistent-return */
/* eslint-disable default-case */
/* eslint-disable import/no-unresolved */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import ModalSection from '../ModalSection';
import RetailerModel from '../../../../../components/Retailers/RetailerModel';
import ProductSyncCancelModal from './ProductSyncCancelModal';
import SectionModel from '../../../../models/SectionModel';
import { SIDEBAR_ACTIONS } from '../../../../../constants/actionsConstants';
import RightModalLayout from '../../../../layouts/Modal/RightModalLayout';
import ToggleSwitchMC from '../../../../components/ToggleSwitchMC';
import QuillComponent from '../QuillComponent';
import CustomIcon from '../../../../components/CustomIconMC';
import ButtonComponent from '../../../../components/ButtonMC';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const ProductModal = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);

  const [saving, setSaving] = useState(false);
  const [productSyncEnabled, setProductSyncEnabled] = useState(retailer_info.mia_products_sync);
  const [miaProductsSynced, setMiaProductsSynced] = useState(retailer_info.mia_products_synced);
  const [isSyncing, setIsSyncing] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [pendingToggleValue, setPendingToggleValue] = useState(null);
  const [prompt, setPrompt] = useState('');

  useEffect(() => {
    setMiaProductsSynced(retailer_info.mia_products_synced);
    setIsSyncing(miaProductsSynced === 'pending');
  }, [retailer_info.mia_products_sync, retailer_info.mia_products_synced]);

  useEffect(() => {
    if (productSyncEnabled) {
      loadPrompt();
    }
  }, [productSyncEnabled]);

  const toggleProductModal = () => {
    dispatch({ type: SIDEBAR_ACTIONS.CLOSE_SIDEBAR });
  };

  const refreshRetailerInfo = () => { };

  const loadPrompt = () => {
    SectionModel.getByType('product_prompt').then((res) => {
      setPrompt(res.product_prompt || '');
      setSaving(false);
    }).catch((err) => {
      setSaving(false);
      showtoast(err.error || t('config.error_loading'));
    });
  };

  const params = {
    retailer: {
      mia_products_sync: productSyncEnabled,
    }
  };

  const onSubmit = () => {
    setSaving(true);
    if (!productSyncEnabled) {
      saveToogleSyncMiaProduct();
      return;
    }

    SectionModel.update({ type: 'product_prompt', product_prompt: prompt }, csrfToken)
      .then(saveToogleSyncMiaProduct)
      .catch((error) => {
        setSaving(false);
        showtoast(error.message || t('config.save_error'));
      });
  };

  const saveToogleSyncMiaProduct = () => {
    RetailerModel.toggleSyncMiaProduct(params).then(() => {
      showtoast(t('config.save_success'));
      setSaving(false);
      refreshRetailerInfo();
      toggleProductModal(null);
    }).catch((error) => {
      setSaving(false);
      showtoast(error.message || t('config.save_error'));
    });
  };

  const toggleProductSync = () => {
    if (isSyncing) return;

    const newValue = !productSyncEnabled;

    if (!newValue) {
      setPendingToggleValue(newValue);
      setIsCancelModalOpen(true);
    } else {
      setProductSyncEnabled(newValue);
    }
  };

  const handleCancelConfirm = () => {
    setProductSyncEnabled(pendingToggleValue);
    setIsCancelModalOpen(false);
  };

  const handleCancelDismiss = () => {
    setIsCancelModalOpen(false);
  };

  const handleSyncProducts = () => {
    if (window.confirm(t('config.products.sync_confirm'))) {
      setIsSyncing(true);
      RetailerModel.sync_mia_products()
        .then(() => {
          setMiaProductsSynced('pending');
          refreshRetailerInfo();
        })
        .catch((error) => {
          setIsSyncing(false);
          setMiaProductsSynced('sync_failed');
          showtoast(error.message || t('config.save_error'));
        });
    }
    return false;
  };

  const getSyncMessage = (status) => {
    switch (status) {
      case 'pending':
        return { message: t('config.products.sync_pending'), type: 'info' };
      case 'complete':
        return { message: t('config.products.sync_complete'), type: 'success' };
      case 'sync_failed':
        return { message: t('config.products.sync_failed'), type: 'info' };
    }
  };

  const renderSyncStatus = () => {
    const syncMessage = getSyncMessage(miaProductsSynced);
    if (!syncMessage) return null;

    const { message, type } = syncMessage;
    const isSuccess = type === 'success';

    return (
      <div className={`tw-flex tw-items-center ${isSuccess ? 'tw-bg-green-50' : 'tw-bg-yellow-50'} tw-leading-[18px] tw-font-semibold tw-px-4 tw-py-2 tw-rounded-xl tw-gap-2`}>
        {isSuccess && (<CustomIcon name="checkmark-circle-2-fill" className="tw-text-green-500 tw-mt-[3px]" />)}
        {!isSuccess && (<CustomIcon name="info-outline" className="tw-text-yellow-600 tw-mt-[3px]" />)}
        <span className={`tw-font-medium tw-text-xs ${isSuccess ? 'tw-text-green-500' : 'tw-text-yellow-600'}`}>{message}</span>
      </div>
    );
  };

  const updateInfo = (value) => {
    setPrompt(value);
  };

  return (
    <div className="tw-wrapper">
      <RightModalLayout
        title={t('config.products.modal_title')}
        submitAction={onSubmit}
        submitLabel={t('guides.sidebar_create.update')}
        submitDisabled={saving}
      >
        <div className="tw-flex tw-flex-col tw-gap-6">
          <div className="tw-flex tw-flex-col">
            <ToggleSwitchMC
              isOn={productSyncEnabled}
              handleToggle={toggleProductSync}
              label={t('config.products.section_1_title')}
              labelClassName="!tw-font-semibold"
            />
            <span className="tw-text-gray-950 tw-text-xs">{t('config.products.section_1_description')}</span>
          </div>

          {productSyncEnabled && (
            <div className="tw-flex tw-gap-2">
              <div className="tw-flex tw-flex-wrap">
                <div className="tw-w-auto">
                  {renderSyncStatus()}
                </div>
              </div>

              <div className="tw-flex tw-flex-wrap">
                <div className="tw-w-full">
                  {[null, 'not_synced', 'sync_failed', 'complete'].includes(miaProductsSynced) && !isSyncing && (
                    <ButtonComponent
                      text={
                        [null, 'not_synced'].includes(miaProductsSynced)
                          ? t('config.products.sync_button')
                          : t('config.products.sync_again_button')
                      }
                      onClick={handleSyncProducts}
                      className="!tw-text-sm !tw-font-medium !tw-py-2"
                    />
                  )}
                </div>
              </div>
            </div>
          )}

          <ModalSection
            title={t('config.products.section_2_title')}
            description={t('config.products.section_2_description')}
            afterContent={(
              <QuillComponent
                name="prompt"
                value={prompt || ''}
                onChange={updateInfo}
                style={{ height: 'calc(100vh - 400px)' }}
              />
            )}
          />
        </div>

        <ProductSyncCancelModal
          isOpen={isCancelModalOpen}
          onClose={handleCancelDismiss}
          onConfirm={handleCancelConfirm}
          loading={false}
        />
      </RightModalLayout>
    </div>
  );
};

export default ProductModal;
