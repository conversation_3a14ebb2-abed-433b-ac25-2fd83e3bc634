import React from 'react';
import { useTranslation } from 'react-i18next';
import { GUIDE_OPTIONS } from './Types/GuidesOptionsSource';
import IconMC from '../../../../components/IconMC';

const GuideOptionsMC = ({ onClick }) => {
  const { t } = useTranslation('ChatbotIA');

  return (
    <>
      {GUIDE_OPTIONS.map((option) => (
        <div
          key={option.type}
          className="tw-flex tw-flex-row tw-rounded-xl tw-py-3 tw-px-4 tw-cursor-pointer tw-border tw-border-gray-200"
          onClick={() => onClick(option.route)}
        >
          <div className="tw-flex tw-items-center tw-gap-2">
            <div className="tw-flex tw-items-center">
              <div className="tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-[5px] tw-bg-blue-50">
                <IconMC name={option.icon} className="tw-text-blue-500" size="xl" />
              </div>
            </div>
            <div className="tw-flex tw-flex-col tw-gap-1">
              <span className="tw-font-semibold tw-text-gray-950 -mr-[10px]">
                {t(option.title)}
              </span>
              <span className="tw-font-regular tw-text-gray-950 tw-text-xs">
                {t(option.description)}
              </span>
            </div>
            <div className="tw-w-[30px] tw-flex tw-items-center">
              <span className="tw-text-gray-700 tw-text-base">{">"}</span>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default GuideOptionsMC;
