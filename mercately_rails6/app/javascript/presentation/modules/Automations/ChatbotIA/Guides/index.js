import React, { useEffect } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { isEmpty } from "lodash";
import { getRetailerInfo } from "../../../../../actions/retailerUsersActions";
import EmptyScreen from "../EmptyScreen";
import GuidesList from "./GuidesList";
import NavbarLayoutMC from "../../../../layouts/NavbarLayoutMC";

const Guides = () => {
  const { retailer_info } = useSelector((reduxState) => reduxState.retailerUsersReducer);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getRetailerInfo());
  }, []);

  return (
    <NavbarLayoutMC>
      <div className="tw-wrapper">
        <div className="tw-px-5 tw-py-5 tw-w-full tw-bg-white tw-overflow-y-auto tw-h-[calc(100vh-4rem)]">
          {(!isEmpty(retailer_info) && !retailer_info.chatbot_ai) && (<EmptyScreen />)}
          {retailer_info.chatbot_ai && (<GuidesList miaGuidesIntegrated={retailer_info.mia_guides_integrated} />)}
        </div>
      </div>
    </NavbarLayoutMC>
  );
};

export default Guides;
