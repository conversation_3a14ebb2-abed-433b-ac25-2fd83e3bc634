import React, { useRef, useEffect, useState } from "react";
import { useParams, useSearchParams } from 'react-router-dom';
import { isEmpty } from "lodash";
import { useTranslation } from 'react-i18next';

import customHistory from '../../../../../customHistory';
import GuideModel from '../../../../models/GuideModel';
import ManageGuidesHeader from './ManageGuidesHeader';

import { GUIDE_TYPE_CONFIG } from './Types/GuideTypeConfig';
import { Text, QuestionsAndAnswers, WebSite } from './Types';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const ManageGuides = () => {
  const [searchParams] = useSearchParams();
  const [type, setType] = useState(searchParams.get('type') || 'text');
  const { newGuide, isFetching } = useFetchGuide();
  const [selectedTopics, setSelectedTopics] = useState([]);
  const [selectedUrls, setSelectedUrls] = useState([]);
  const [guide, setGuide] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [errors, setErrors] = useState({});
  const [openImproveGuideModal, setOpenImproveGuideModal] = useState(false);
  const [questions, setQuestions] = useState([{ id: 1, question: '', answer: '' }]);
  const [attachedFiles, setAttachedFiles] = useState({});
  const [specificInstructions, setSpecificInstructions] = useState([]);
  const quillRef = useRef(null);
  const typeConfig = GUIDE_TYPE_CONFIG[type];
  const [urlToScrape, setUrlToScrape] = useState('');
  const [removedUrlIds, setRemovedUrlIds] = useState([]);
  const { id } = useParams();

  const { t } = useTranslation('ChatbotIA');

  useEffect(() => {
    if (!isEmpty(newGuide)) {
      setType(newGuide.guide_type || 'text');
    }
  }, [newGuide]);

  useEffect(() => {
    setGuide({ ...newGuide });

    if (!isEmpty(newGuide)) {
      if (newGuide.topics) {
        setSelectedTopics(newGuide.topics.map((t) => ({ value: t, label: t })));
      }
      if (newGuide.questions) {
        setQuestions(newGuide.questions);
      }
      if (newGuide.attachedFiles) {
        setAttachedFiles(newGuide.attachedFiles);
      }
      if (newGuide.specific_instructions) {
        setSpecificInstructions(newGuide.specific_instructions);
      }
      if (newGuide.selectedUrls) {
        setSelectedUrls(newGuide.selectedUrls);
      }
      if (newGuide.urlToScrape) {
        setUrlToScrape(newGuide.urlToScrape);
      }
    }
  }, [newGuide]);

  const isValidForm = () => {
    let isValid = true;
    const newErrors = {};

    if (!guide.name?.trim()) {
      isValid = false;
      newErrors.name = 'Requerido';
    }

    if (typeConfig) {
      const typeErrors = typeConfig.validate({
        guide,
        selectedTopics,
        quillRef,
        questions,
        attachedFiles,
        selectedUrls,
        urlToScrape
      });

      if (Object.keys(typeErrors).length > 0) {
        isValid = false;
        Object.assign(newErrors, typeErrors);
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSave = () => {
    if (!isValidForm()) {
      return;
    }

    let dataToSend;

    if (typeConfig) {
      const typeData = typeConfig.prepareFormData({
        guide,
        selectedUrls,
        selectedTopics,
        questions,
        attachedFiles,
        specificInstructions,
        urlToScrape,
        removedUrlIds
      });

      if (typeData instanceof FormData) {
        dataToSend = typeData;
        if (guide.id) {
          dataToSend.append('id', guide.id);
        }
        dataToSend.append('name', guide.name);
        dataToSend.append('guide_type', type);
      } else {
        dataToSend = {
          ...typeData,
          ...(guide.id && { id: guide.id }),
          guide_type: type
        };
      }
    }

    setSubmitted(true);

    GuideModel.save(dataToSend, csrfToken)
      .then((res) => {
        setTimeout(() => {
          showtoast(t('guides.save_guide'));
          setSubmitted(false);
          backTo()
        }, 1000);
      })
      .catch((error) => {
        setSubmitted(false);
        showtoast(t('guides.load_error'));
      });
  };

  const handleInputChange = (name, value) => {
    updateInfo(name, value);
  };

  const updateInfo = (name, value) => {
    setGuide((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const backTo = () => {
    customHistory.back();
  };

  return (
    <div className="tw-wrapper">
      <div className="sm:tw-ml-[60px] max-sm:tw-ml-0 tw-bg-white tw-flex tw-min-h-[calc(100vh-60px)]">
        <div className="tw-w-full tw-px-[15px] tw-mt-4">
          {!isFetching && (
            <div className="tw-flex tw-flex-col">
              <ManageGuidesHeader
                backTo={backTo}
                handleSave={handleSave}
                submitted={submitted}
                guide={guide}
                guideType={type}
                handleInputChange={handleInputChange}
                errors={errors}
              />
              <div className="tw-flex tw-flex-row tw-flex-wrap tw-mt-4">
                {type === 'text' && (
                  <>
                    <div className="tw-w-full sm:tw-w-1/2 md:tw-w-2/3 tw-px-[15px]">
                      <Text
                        guide={guide}
                        selectedTopics={selectedTopics}
                        setSelectedTopics={setSelectedTopics}
                        specificInstructions={specificInstructions}
                        setSpecificInstructions={setSpecificInstructions}
                        errors={errors}
                        quillRef={quillRef}
                        updateInfo={updateInfo}
                        openImproveGuideModal={openImproveGuideModal}
                        setOpenImproveGuideModal={setOpenImproveGuideModal}
                      />
                    </div>
                    <div className="tw-w-full sm:tw-w-1/2 md:tw-w-1/3 tw-px-[15px]">
                      <div className="tw-w-full tw-p-6 tw-bg-blue-50 tw-rounded-xl tw-text-gray-950 tw-border tw-border-blue-300">
                        <p className="tw-font-semibold">{t('guides.setup_instructions_title')}</p>
                        <p>{t('guides.setup_instructions_description')}</p>
                        <p className="tw-font-semibold">{t('guides.setup_instructions_sample')}</p>
                        <p>{t('guides.setup_instructions_sample_description')}</p>
                        <p className="tw-font-semibold">{t('guides.setup_instructions_disclaimer')}</p>
                        <p className="tw-font-normal">{t('guides.setup_instructions_disclaimer_description')}</p>
                        <p>{t('guides.setup_instructions_disclaimer_description_2')}</p>
                        <p>
                          <span className="tw-font-semibold">{t('guides.setup_instructions_recomendation')}</span>
                          {t('guides.setup_instructions_recomendation_text')}
                        </p>
                        <p>{t('guides.setup_instructions_recomendation_description')}</p>
                      </div>
                    </div>
                  </>
                )}
                {type === 'q&a' && (
                  <div className="tw-w-full sm:tw-w-1/2 md:tw-w-2/3 tw-px-[15px]">
                    <QuestionsAndAnswers
                      questions={questions}
                      setQuestions={setQuestions}
                      attachedFiles={attachedFiles}
                      setAttachedFiles={setAttachedFiles}
                      errors={errors}
                      setErrors={setErrors}
                    />
                  </div>
                )}
                {type === 'website' && (
                  <div className="tw-w-full sm:tw-w-1/2 md:tw-w-2/3 tw-px-[15px]">
                    <WebSite
                      selectedUrls={selectedUrls}
                      setSelectedUrls={setSelectedUrls}
                      urlToScrape={urlToScrape}
                      setUrlToScrape={setUrlToScrape}
                      errors={errors}
                      isSubmitting={submitted}
                      removedUrlIds={removedUrlIds}
                      setRemovedUrlIds={setRemovedUrlIds}
                      handleSave={handleSave}
                      isEditing={!!id}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const useFetchGuide = () => {
  const { id } = useParams();
  const [newGuide, setNewGuide] = useState({});
  const [isFetching, setIsFetching] = useState(true);

  useEffect(() => {
    if (!id) {
      setIsFetching(false);
      return;
    }

    GuideModel.findById(id).then((res) => {
      const { guide } = res;
      const typeConfig = GUIDE_TYPE_CONFIG[guide.guide_type];

      if (typeConfig && typeConfig.formatFetchedData) {
        const formattedGuide = typeConfig.formatFetchedData(guide);
        setNewGuide(formattedGuide);
      } else {
        setNewGuide(guide);
      }

      setIsFetching(false);
    }).catch((error) => {
      setIsFetching(false);
    });
  }, [id]);

  return { newGuide, isFetching };
};

export default ManageGuides;
