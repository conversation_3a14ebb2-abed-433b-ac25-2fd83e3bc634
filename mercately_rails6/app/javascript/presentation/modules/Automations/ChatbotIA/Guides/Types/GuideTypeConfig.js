import { isEmpty } from 'lodash';
import { t } from 'i18next';

export const GUIDE_TYPE_CONFIG = {
  text: {
    validate: ({ selectedTopics, quillRef }) => {
      const errors = {};

      const editor = quillRef.current.getEditor();
      const editorText = editor.getText().trim();

      if (!editorText) {
        errors.instructions = t('chatbot_ai.guides.empty_error');
      }

      if (isEmpty(selectedTopics)) {
        errors.topics = t('chatbot_ai.guides.empty_error');
      }

      return errors;
    },
    prepareFormData: ({ guide, selectedTopics, specificInstructions = [] }) => {
      const formData = new FormData();
      formData.append('instructions', guide.instructions);
      formData.append('topics', JSON.stringify(selectedTopics.map(t => t.value)));

      const formattedSpecificInstructions = (specificInstructions || []).map((instruction) => ({
        instruction: instruction.instruction,
        instruction_type: instruction.instruction_type,
        instruction_id: instruction.instruction_id,
        files: instruction.files.map((file) =>
          file instanceof File ? file.name : file.filename
        )
      }));

      formData.append('specific_instructions', JSON.stringify(formattedSpecificInstructions));

      (specificInstructions || []).forEach((instruction) => {
        instruction.files.forEach((file) => {
          if (file instanceof File) {
            formData.append('files[]', file);
          }
        });
      });

      return formData;
    },
    formatFetchedData: (guide) => {
      const { guide_id, name, instructions, topics = [], specific_instructions = [] } = guide;

      const formattedSpecificInstructions = specific_instructions.map((instruction) => ({
        instruction: instruction.instruction,
        instruction_type: instruction.instruction_type,
        instruction_id: instruction.instruction_id,
        files: instruction.files?.map((file) => ({
          filename: file.filename,
          url: file.url,
          size: file.size,
          type: file.content_type
        })) || []
      }));

      return {
        id: guide_id,
        name,
        instructions,
        topics,
        specific_instructions: formattedSpecificInstructions
      };
    }
  },
  'q&a': {
    validate: ({ questions }) => {
      const errors = {};

      if (!questions?.length) {
        errors.qa = t('chatbot_ai.guides.empty_question_error');
      } else {
        questions.forEach((q) => {
          if (!q.question?.trim()) {
            errors[`question_${q.id}`] = t('chatbot_ai.guides.empty_error');
          }
          if (!q.answer?.trim()) {
            errors[`answer_${q.id}`] = t('chatbot_ai.guides.empty_error');
          }
        });
      }

      return errors;
    },
    formatFetchedData: (guide) => {
      const { guide_id, name, guide_type, qa_instructions = [] } = guide;

      const questions = qa_instructions.map((instruction, index) => ({
        id: index + 1,
        question: instruction.question,
        answer: instruction.answer,
        instruction_type: instruction.instruction_type,
        instruction_id: instruction.instruction_id
      }));

      const attachedFiles = {};
      qa_instructions.forEach((instruction, index) => {
        if (instruction.files && instruction.files.length > 0) {
          attachedFiles[index + 1] = {
            filename: instruction.files[0].filename,
            url: instruction.files[0].url,
            size: instruction.files[0].size,
            type: instruction.files[0].content_type
          };
        }
      });

      return {
        id: guide_id,
        name,
        guide_type,
        questions,
        attachedFiles
      };
    },
    prepareFormData: ({ questions, attachedFiles }) => {
      const formData = new FormData();

      const qa_pairs = questions.map(question => ({
        question: question.question.trim(),
        answer: question.answer.trim(),
        instruction_type: attachedFiles[question.id] ? 'image' : 'text',
        instruction_id: question.instruction_id || null,
        files: attachedFiles[question.id] ?
          [attachedFiles[question.id] instanceof File ?
            attachedFiles[question.id].name :
            attachedFiles[question.id].filename]
          : []
      }));

      formData.append('qa_pairs', JSON.stringify(qa_pairs));

      Object.entries(attachedFiles).forEach(([_, file]) => {
        if (file instanceof File) {
          formData.append('files[]', file);
        }
      });

      return formData;
    }
  },
  website: {
    validate: ({ selectedUrls, urlToScrape }) => {
      const errors = {};

      if (!urlToScrape || urlToScrape.trim() === '') {
        errors.urlToScrape =  t('chatbot_ai.guides.empty_error');
      }

      if (!selectedUrls || selectedUrls.length === 0) {
        errors.urls =  t('chatbot_ai.guides.empty_error');
      }

      return errors;
    },
    prepareFormData: ({ selectedUrls = [], name, guide, urlToScrape, removedUrlIds = [] }) => {
      const urls = Array.isArray(selectedUrls)
        ? selectedUrls
            .filter(urlObj => urlObj.selected === true)
            .map(urlObj => urlObj.url)
        : [];

      const dataToSend = {
        name: guide?.name || name,
        guide_type: 'website',
        url: urlToScrape || guide?.urlToScrape,
        urls,
        urls_id: removedUrlIds
      };

      return dataToSend;
    },
    formatFetchedData: (guide) => {
      const { guide_id, name, guide_type, website_instructions = [], url } = guide;

      const formattedData = {
        id: guide_id,
        name,
        guide_type,
        urlToScrape: url,
        selectedUrls: website_instructions.map(instruction => ({
          url: instruction.url,
          status: instruction.status,
          url_id: instruction.url_id
        }))
      };

      return formattedData;
    }
  }
};
