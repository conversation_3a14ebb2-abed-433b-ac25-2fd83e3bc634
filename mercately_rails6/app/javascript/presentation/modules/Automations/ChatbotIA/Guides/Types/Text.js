import React from "react";
import { useTranslation } from 'react-i18next';
import { without } from "lodash";
import CreatableSelect from 'react-select/creatable';
import { useDispatch } from "react-redux";
import GuideQuill from '../GuideQuill';
import SpecificInstructionsList from './SpecificInstructionsList';
import ButtonMC from '../../../../../components/ButtonMC';
import { SIDEBAR_ACTIONS } from "../../../../../../constants/actionsConstants";

const style = {
  control: (base, state) => ({
    ...base,
    borderColor: '#F6F8F9',
    backgroundColor: '#F6F8F9',
    boxShadow: state.isFocused ? '0 0 0 1px #F6F8F9' : 'none',
    '&:hover': { borderColor: '#F6F8F9' },
    minHeight: '40px',
    paddingLeft: '0px'
  }),
  multiValue: (base) => ({
    ...base,
    backgroundColor: '#FFF',
    borderRadius: '5px',
    padding: '2px 6px',
    border: '1px solid #D6DCE1'
  }),
  multiValueLabel: (base) => ({
    ...base,
    color: '#6A7E8D',
    fontWeight: '500',
    fontSize: '12px'
  }),
  multiValueRemove: (base) => ({
    ...base,
    color: '#6A7E8D',
    cursor: 'pointer',
    ':hover': {
      backgroundColor: '#FFF'
    }
  }),
  valueContainer: (base) => ({
    ...base,
    paddingLeft: 0
  })
};

const Text = ({
  guide,
  selectedTopics,
  setSelectedTopics,
  specificInstructions,
  setSpecificInstructions,
  errors,
  quillRef,
  updateInfo
}) => {
  const dispatch = useDispatch();
  const { t } = useTranslation('ChatbotIA');

  const handleSelectTopic = (options) => {
    setSelectedTopics(options);
  };

  const removeSelectedTopic = (topic) => {
    setSelectedTopics(without(selectedTopics, topic));
  };

  const toggleImproveGuideModal = () => {
    dispatch({
      type: SIDEBAR_ACTIONS.OPEN_SIDEBAR,
      payload: {
        componentKey: 'ImproveGuideModal',
        props: {
          guide,
          updateInfo
        }
      }
    });
  };

  const toggleSpecificInstructionsModal = (index = null) => {
    dispatch({
      type: SIDEBAR_ACTIONS.OPEN_SIDEBAR,
      payload: {
        componentKey: 'SpecificInstructionsModal',
        props: {
          editingIndex: index,
          setSpecificInstructions,
          specificInstructions
        }
      }
    });
  };

  const deleteInstruction = (index) => {
    const updatedInstructions = specificInstructions.filter((_, i) => i !== index);
    setSpecificInstructions(updatedInstructions);
  };

  return (
    <>
      <div className="tw-w-full tw-px-0 tw-text-gray-950">
        <span className="tw-text-sm tw-font-semibold">{t('guides.text.title')}</span>
        <p className="tw-text-xs tw-fond-regular tw-mb-2">{t('guides.text.description')}</p>
      </div>

      <div className="tw-flex tw-flex-col tw-w-full tw-px-0 tw-gap-1">
        <span className="tw-font-regular tw-text-xs tw-text-blue-500">{t('guides.text.info')}</span>
        <GuideQuill
          ref={quillRef}
          value={guide.instructions || ''}
          onChange={(value) => updateInfo('instructions', value)}
          improveGuideButtonFunction={toggleImproveGuideModal}
          error={errors.instructions}
        />
      </div>

      <div className="tw-w-full tw-px-0 tw-mt-5">
        <span className="tw-text-sm tw-font-semibold tw-text-gray-950">{t('guides.specific_instructions_title')}</span>
        <p className="tw-text-xs">{t('guides.specific_instructions_description')}</p>
        <SpecificInstructionsList
          specificInstructions={specificInstructions}
          openEditModal={toggleSpecificInstructionsModal}
          deleteInstruction={deleteInstruction}
        />
        <div className="tw-mt-2.5">
          <ButtonMC
            text={t('guides.add_instructions_button')}
            variant="SECONDARY"
            onClick={() => toggleSpecificInstructionsModal(null)}
            outlined
            className="!tw-font-medium"
          />
        </div>
      </div>

      <div className="tw-w-full tw-px-0 tw-mt-5">
        <span className="tw-text-sm tw-font-semibold tw-text-gray-950">{t('guides.related_topics')}</span>
        <p className="tw-text-xs tw-mb-0">{t('guides.related_topics_description')}</p>
        <span className="tw-ml-2.5 tw-text-red-500 tw-text-xs">{errors.topics}</span>
      </div>

      <div className="tw-w-full tw-px-0 tw-mb-[80px]">
        <div className="tw-mb-4 tw-bg-gray-50 tw-rounded-xl tw-pl-4 tw-pt-[2px] tw-pb-1 tw-mb-0 tw-border tw-border-gray-200">
          <label htmlFor="related_topics" className="tw-text-xs tw-text-blue-500 tw-mb-0">{t('guides.related_topics')}</label>
          <CreatableSelect
            isMulti
            value={selectedTopics}
            onChange={(newValue) => handleSelectTopic(newValue)}
            styles={style}
            components={{
              IndicatorSeparator: () => null
            }}
          />
        </div>
      </div>
    </>
  );
};

export default Text;
