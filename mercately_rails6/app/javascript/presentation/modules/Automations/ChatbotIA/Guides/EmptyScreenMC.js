/* eslint-disable import/no-unresolved */
import React from "react";
import { useTranslation } from "react-i18next";
import Icon from 'images/chatbot_ai/mia-guides.svg';
import ButtonMC from '../../../../components/ButtonMC';

const EmptyScreenMC = ({
  handleCreateGuide

}) => {
  const { t } = useTranslation('ChatbotIA');

  return (
    <div className="tw-flex tw-justify-center tw-py-10 tw-px-4">
      <div className="tw-flex tw-flex-col md:tw-flex-row tw-items-center md:tw-items-start tw-w-full tw-max-w-6xl tw-p-6 tw-max-w-[610px]">
        <div className="tw-flex-1 tw-p-3 tw-text-justify">
          <div className="tw-font-bold tw-text-base tw-text-gray-950">{t('guides.emptyScreen.title')}</div>
          <div className="tw-mt-2 tw-text-wrap">{t('guides.emptyScreen.description')}</div>
          <div className="tw-mt-3">
            <ButtonMC
              text={t('guides.emptyScreen.buttonCreateNewLabel')}
              variant="SECONDARY"
              onClick={handleCreateGuide}
              outlined
            />
          </div>
        </div>
      </div>

      <div className="tw-mt-6 md:tw-mt-0 md:tw-ml-6">
        <img src={Icon} className="tw-w-[192px]" alt="WhatsApp" />
      </div>
    </div>
  );
};

export default EmptyScreenMC;
