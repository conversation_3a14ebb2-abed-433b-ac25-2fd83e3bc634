import React from 'react';
import { useTranslation } from 'react-i18next';

import DocumentPdfOutline16 from 'images/document-pdf-outline-16.svg';
import PreviewPdfInstruction from "../PreviewPdfInstruction";
import CustomIcon from '../../../../../components/CustomIconMC';

const SpecificInstructionsList = ({ specificInstructions, openEditModal, deleteInstruction }) => {
  const { t } = useTranslation('ChatbotIA');

  return (
    <div>
      {specificInstructions.map((instruction, index) => {
        const { files, instruction: text, instruction_type } = instruction;
        const isImage = instruction_type === 'image';
        const iconElement = isImage ? (
          <CustomIcon name="image-outline" className="tw-text-2xl" />
        ) : (
          <img className="tw-w-[25px]" src={DocumentPdfOutline16} alt="doc-icon" />
        );

        return (
          <div key={index} className="tw-pl-5 tw-rounded-xl tw-py-1.5 tw-mt-2 tw-border tw-border-gray-300">
            <div className="tw-flex tw-mt-[5px] tw-justify-between">
              <div className="tw-flex">
                <div className="tw-mr-2">{iconElement}</div>
                <div className="">
                  <span className="tw-font-semibold tw-text-gray-950 tw-text-sm">
                    {isImage
                      ? t('guides.specific_instruction_image_title')
                      : t('guides.specific_instruction_doc_title')}
                  </span>
                </div>
              </div>
              <div>
                <div className="tw-w-1/2 tw-ml-[70px] tw-flex tw-justify-end tw-items-center tw-relative">
                  <div
                    className="tw-cursor-pointer tw-py-1 tw-rounded-[8px]"
                    onClick={(e) => {
                      e.stopPropagation();
                      openEditModal(index);
                    }}
                  >
                    <CustomIcon name="open-pencil" className="!tw-text-gray-950 !tw-text-base" />
                  </div>
                  <div
                    className="tw-cursor-pointer tw-px-[10px] tw-py-1 tw-rounded-[8px]"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteInstruction(index);
                    }}
                  >
                    <CustomIcon name="trash-2-outline" className="tw-text-gray-950" />
                  </div>
                </div>
              </div>
            </div>

            <div className="tw-items-left">
              <p className="tw-text-sm">
                <span className="tw-font-semibold tw-text-sm tw-text-gray-950">{t('guides.instruction')}:</span> {text}
              </p>
            </div>

            {isImage ? (
              <div
                key={`preview-${index}`}
                className="tw-flex tw-mt-[5px] tw-w-[90%] tw-items-left tw-max-w-full tw-flex-wrap tw-overflow-hidden"
              >
                {files.map((file, fileIndex) => {
                  const isExistingFile = file.url && file.filename;
                  const previewSrc = isExistingFile ? file.url : URL.createObjectURL(file);

                  return (
                    <img
                      key={`preview-${index}-image-${fileIndex}`}
                      src={previewSrc}
                      alt={`preview-${index}-image-${fileIndex}`}
                      className="tw-w-[150px] tw-h-[150px] tw-object-cover tw-rounded-[10px] tw-mb-5 tw-mr-5"
                    />
                  );
                })}
              </div>
            ) : (
              <div className='tw-rounded-xl tw-py-1.5 tw-mt-2 tw-flex tw-flex-wrap' >
                {files.map((file, fileIndex) => {
                  const isExistingFile = file.url && file.filename;
                  const previewSrc = isExistingFile ? file.url : URL.createObjectURL(file);
                  const fileName = isExistingFile ? file.filename : file.name

                  return (
                    <div key={`preview-${index}-${fileIndex}`} className="tw-flex tw-rounded-xl tw-py-[15px] tw-px-5 tw-border tw-border-gray-300 tw-mr-[15px] tw-mb-[15px]">
                      <div>
                        <PreviewPdfInstruction loadedFile={previewSrc} />
                      </div>
                      <div className="tw-pl-[2px]">
                        <p className="tw-font-semibold tw-text-gray-950 tw-text-xs -tw-mb-[10px]">{fileName}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default SpecificInstructionsList;
