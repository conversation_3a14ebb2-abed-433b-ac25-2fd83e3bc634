import React, { useEffect, useState } from 'react';
import LoadingMC from '../../../../components/LoadingMC';

// Import and configure pdfjs
const pdfjs = require('pdfjs-dist/legacy/build/pdf');

pdfjs.GlobalWorkerOptions.workerSrc = URL.createObjectURL(
  new Blob(
    [`importScripts('${require('pdfjs-dist/legacy/build/pdf.worker.mjs')}');`],
    { type: 'application/javascript' }
  )
);

const PreviewPdfInstruction = ({ loadedFile }) => {
  const [fileToImage, setFileToImage] = useState(null);

  useEffect(() => {
    if (!loadedFile) return;

    const isBlobUrl = loadedFile.startsWith('blob:');

    const convertPdfFileToImage = async () => {
      const canvasElement = document.createElement('canvas');
      const pageNumber = 1;

      await renderPDFToCanvas(loadedFile, pageNumber, canvasElement);

      // Convert the canvas to an image
      const imgDataUrl = canvasElement.toDataURL('image/png');
      setFileToImage(imgDataUrl);
    };

    const renderPDFToCanvas = async (pdfUrl, pageNum, canvasElement) => {
      const loadingTask = pdfjs.getDocument(pdfUrl);
      const pdfDocument = await loadingTask.promise;

      const page = await pdfDocument.getPage(pageNum);

      const viewport = page.getViewport({ scale: 0.5 });
      canvasElement.width = viewport.width;
      canvasElement.height = viewport.height;

      const canvasContext = canvasElement.getContext('2d');
      const renderContext = {
        canvasContext,
        viewport,
      };

      await page.render(renderContext).promise;
    };

    if (isBlobUrl) {
      convertPdfFileToImage();
    } else {
      setFileToImage(loadedFile);
    };
  }, [loadedFile]);

  return fileToImage ? (
    <img
      src={fileToImage}
      alt="PDF Preview"
      className="tw-w-[40px] tw-h-[40px] tw-object-cover"
    />
  ) : (
    <p>
      <LoadingMC loading />
    </p>
  );
};

export default PreviewPdfInstruction;
