/* eslint-disable import/prefer-default-export */
export const GUIDE_OPTIONS = [
  {
    type: 'text',
    icon: 'text-outline',
    title: 'guides.type_options.text.title',
    description: 'guides.type_options.text.description',
    get route() {
      return `/retailers/${ENV.SLUG}/chatbot_ai/guides/new?type=${this.type}`;
    }
  },
  {
    type: 'q&a',
    icon: 'question-mark-circle-outline',
    title: 'guides.type_options.questions_and_answers.title',
    description: 'guides.type_options.questions_and_answers.description',
    get route() {
      const encodedType = encodeURIComponent(this.type);
      return `/retailers/${ENV.SLUG}/chatbot_ai/guides/new?type=${encodedType}`;
    }
  },
  {
    type: 'website',
    icon: 'web',
    title: 'guides.type_options.website.title',
    description: 'guides.type_options.website.description',
    get route() {
      return `/retailers/${ENV.SLUG}/chatbot_ai/guides/new?type=${this.type}`;
    }
  }
];
