import abstractModel from '../../core/MercatelyModel';

const resource = '/api/mia/guides';

const csrfToken = document.querySelector('[name=csrf-token]').content;

const model = abstractModel(resource);
const parentSave = model.save;

model.optimize = async (instructions) => {
  const newResource = `${resource}/optimize`;

  return parentSave(
    { instructions: instructions },
    { path: newResource, csrfToken }
  );
};

model.scrape = async (url) => {
  const newResource = `${resource}/scrape`;

  return parentSave(
    url,
    { path: newResource }
  );
};

export default model;
