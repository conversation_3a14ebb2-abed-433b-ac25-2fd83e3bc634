import consumer from "./consumer"

class ExportsChannelManager {
  constructor() {
    this.subscription = null;
    this.retailerId = null;
    this.callbacks = {
      onExportStatusUpdate: null,
      onSubscriptionConfirmed: null,
      onError: null
    };
  }

  // Suscribirse al canal de exports
  subscribe(retailerId, callbacks = {}) {
    if (this.subscription) {
      console.log('📡 [EXPORTS CHANNEL] Ya existe suscripción, desuscribiendo...');
      this.unsubscribe();
    }

    this.retailerId = retailerId;
    this.callbacks = { ...this.callbacks, ...callbacks };

    console.log(`📡 [EXPORTS CHANNEL] Suscribiéndose a exports_${retailerId}`);

    this.subscription = consumer.subscriptions.create(
      { 
        channel: "ExportsChannel", 
        retailer_id: retailerId 
      },
      {
        connected: () => {
          console.log('✅ [EXPORTS CHANNEL] Conectado al canal');
        },

        disconnected: () => {
          console.log('❌ [EXPORTS CHANNEL] Desconectado del canal');
        },

        received: (data) => {
          console.log('📨 [EXPORTS CHANNEL] Mensaje recibido:', data);
          this.handleMessage(data);
        },

        rejected: () => {
          console.error('❌ [EXPORTS CHANNEL] Suscripción rechazada');
          if (this.callbacks.onError) {
            this.callbacks.onError('Suscripción rechazada');
          }
        }
      }
    );

    return this.subscription;
  }

  // Manejar mensajes del canal
  handleMessage(data) {
    switch (data.type) {
      case 'subscription_confirmed':
        console.log('✅ [EXPORTS CHANNEL] Suscripción confirmada:', data.message);
        if (this.callbacks.onSubscriptionConfirmed) {
          this.callbacks.onSubscriptionConfirmed(data);
        }
        break;

      case 'export_status_update':
        console.log('🔄 [EXPORTS CHANNEL] Actualización de export recibida:', data.export_status);
        if (this.callbacks.onExportStatusUpdate) {
          this.callbacks.onExportStatusUpdate(data.export_status);
        }
        break;

      case 'pong':
        console.log('🏓 [EXPORTS CHANNEL] Pong recibido');
        break;

      default:
        console.log('❓ [EXPORTS CHANNEL] Mensaje desconocido:', data);
    }
  }

  // Enviar ping para verificar conectividad
  ping() {
    if (this.subscription) {
      console.log('🏓 [EXPORTS CHANNEL] Enviando ping...');
      this.subscription.perform('ping', { timestamp: new Date().toISOString() });
    } else {
      console.warn('⚠️ [EXPORTS CHANNEL] No hay suscripción activa para ping');
    }
  }

  // Desuscribirse del canal
  unsubscribe() {
    if (this.subscription) {
      console.log('📡 [EXPORTS CHANNEL] Desuscribiéndose...');
      this.subscription.unsubscribe();
      this.subscription = null;
      this.retailerId = null;
    }
  }

  // Verificar si está suscrito
  isSubscribed() {
    return this.subscription !== null;
  }

  // Obtener retailer ID actual
  getCurrentRetailerId() {
    return this.retailerId;
  }
}

// Crear instancia singleton
const exportsChannelManager = new ExportsChannelManager();

export default exportsChannelManager;
