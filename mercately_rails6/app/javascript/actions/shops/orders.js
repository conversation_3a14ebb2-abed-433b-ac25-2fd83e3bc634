import { handleRequestErrors } from "../../services/handleRequestError";
import customHistory from "../../customHistory";

export const fetchOrders = (params = {}) => {
  const encodedParams = new URLSearchParams(params).toString();

  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/orders?${encodedParams}`;

  return (dispatch) => {
    dispatch({ type: "GET_ORDERS_REQUEST_START" });
    fetch(endpoint, {
      method: "GET",
      headers: {
        Accept: "application/json, text/plain",

        'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,

        'RETAILER-KEY': ENV.UNIQUE_KEY,
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: "GET_ORDERS", data });
          dispatch({ type: "GET_ORDERS_REQUEST_END" });
        },
        (err) => {
          dispatch({ type: "LOAD_DATA_FAILURE", err });
          dispatch({ type: "GET_ORDERS_REQUEST_END" });
        }
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
        dispatch({ type: "GET_ORDERS_REQUEST_END" });
      });
  };
};

export const fetchOrder = (orderWebId) => {
  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/orders/${orderWebId}`;

  return (dispatch) => {
    fetch(endpoint, {
      method: "GET",
      headers: {
        Accept: "application/json, text/plain",

        'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,

        'RETAILER-KEY': ENV.UNIQUE_KEY,
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => dispatch({ type: "SET_CURRENT_ORDER", data }),
        (err) => dispatch({ type: "LOAD_DATA_FAILURE", err })
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
      });
  };
};

export const createOrder = (body, redirectShowPage = false) => {
  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/orders`;
  return (dispatch) => {
    const multipart = body instanceof FormData;

    const headers = {
      'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,
      'RETAILER-KEY': ENV.UNIQUE_KEY,
      Accept: 'application/json, text/plain, */*'
    };

    if (!multipart) {
      headers['Content-Type'] = 'application/json';
    }

    dispatch({ type: 'START_SAVING_ORDER_REQUEST' });
    fetch(endpoint, {
      method: "POST",
      headers,
      body: multipart ? body : JSON.stringify({ order: body })
    })
      .then(handleRequestErrors)
      .then((responseData) => {
        dispatch({ type: 'TOGGLE_SUBMITTED', submitted: false });
        dispatch({ type: "TOGGLE_UPDATE_ORDER_MODAL", data: { toggle: false, selectedOrder: null } });
        dispatch({ type: 'END_SAVING_ORDER_REQUEST' });
        dispatch({ type: 'INCREMENT_ORDER_COUNT' });
        dispatch({ type: 'SET_ORDER_CREATED', orderCreated: responseData.order, saveAction: 'create' });
        if (redirectShowPage) {
          customHistory.push(`/retailers/${ENV.SLUG}/orders/${responseData.order.web_id}`);
        }
      })
      .catch(() => {
        dispatch({ type: 'END_SAVING_ORDER_REQUEST' });
        dispatch({ type: 'TOGGLE_SUBMITTED', submitted: false });
      });
  };
};

export const updateOrder = (orderWebId, body, redirectShowPage) => {
  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/orders/${orderWebId}`;

  return (dispatch) => {
    const multipart = body instanceof FormData;

    const headers = {
      'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,
      'RETAILER-KEY': ENV.UNIQUE_KEY,
      Accept: 'application/json, text/plain, */*'
    };

    if (!multipart) {
      headers['Content-Type'] = 'application/json';
    }

    fetch(endpoint, {
      method: 'PUT',
      headers,
      body: multipart ? body : JSON.stringify({ order: body })
    })
      .then(handleRequestErrors)
      .then((data) => {
        dispatch({ type: 'TOGGLE_SUBMITTED', submitted: false });

        if (redirectShowPage) {
          customHistory.push(`/retailers/${ENV.SLUG}/orders/${data.order.web_id}`);
        }
      })
      .catch((err) => {
        dispatch({ type: 'TOGGLE_SUBMITTED', submitted: false });
        dispatch({ type: 'LOAD_DATA_FAILURE', err });
      });
  };
};

export const deleteOrder = (orderWebId) => {
  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/orders/${orderWebId}`;
  return (dispatch) => {
    fetch(endpoint, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",

        'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,

        'RETAILER-KEY': ENV.UNIQUE_KEY,
        "X-Access-Level": "read-write"
      }
    })
      .then((res) => res.json())
      .then((data) => {
        dispatch({ type: "DELETE_ORDER", data });
      })
      .catch((err) => {
        dispatch({ type: "LOAD_DATA_FAILURE", err });
      });
  };
};

export const updateOrderStatus = (orderWebId, body, currentTab = 'orders') => {
  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/orders/${orderWebId}`;

  return (dispatch) => {
    return fetch(endpoint, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,
        'RETAILER-KEY': ENV.UNIQUE_KEY,
        'Accept': 'application/json, text/plain, */*'
      },
      body: JSON.stringify({ order: body })
    })
      .then(async (res) => {
        const data = await res.json();
        if (!res.ok) {
          throw {
            status: res.status,
            message: data.errors?.map(error => error.trim()).join(', ') || "Error desconocido"
          };
        }

        data.currentTab = currentTab;
        dispatch({ type: 'UPDATE_ORDER_STATUS', data });

        return data;
      })
      .catch((err) => {
        dispatch({ type: "LOAD_DATA_FAILURE", err });
        return Promise.reject(err);
      });
  };
};

export const refreshOrderStatus = (order) => ({
  type: 'UPDATE_ORDER_STATUS',
  payload: { data: { order } }
});

export const getPendingOrder = () => {
  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/pending_orders`;
  return (dispatch) => {
    fetch(endpoint, {
      method: "GET",
      headers: {
        Accept: "application/json, text/plain",

        'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,

        'RETAILER-KEY': ENV.UNIQUE_KEY,
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => dispatch({ type: "SET_TOTAL_PENDING_ORDERS", data }),
        (err) => dispatch({ type: "LOAD_DATA_FAILURE", err })
      ).catch((error) => {
        if (error.response) {
          alert(error.response);
        } else {
          alert("An unexpected error occurred.");
        }
      });
  };
};

export const toggleSubmitted = (submitted) => ({
  type: "TOGGLE_SUBMITTED",
  submitted
});

export const fetchExportStatus = () => {
  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/orders/export_status`;

  return (dispatch) => {
    fetch(endpoint, {
      method: "GET",
      headers: {
        Accept: "application/json, text/plain",
        'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,
        'RETAILER-KEY': ENV.UNIQUE_KEY,
        "Content-Type": "application/json"
      }
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: "GET_EXPORT_STATUS", data });
        },
        (err) => {
          dispatch({ type: "GET_EXPORT_STATUS_FAILURE", err });
        }
      ).catch((error) => {
        console.error('Export status fetch error:', error);
        dispatch({ type: "GET_EXPORT_STATUS_FAILURE", error });
      });
  };
};

export const startOrderExport = (searchParams) => {
  // 🔧 Debug: Ver qué parámetros se están enviando
  console.log('🔍 [EXPORT-DEBUG] searchParams:', searchParams);
  const encodedParams = new URLSearchParams(searchParams).toString();
  console.log('🔍 [EXPORT-DEBUG] encodedParams:', encodedParams);
  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/orders/export?${encodedParams}`;
  console.log('🔍 [EXPORT-DEBUG] endpoint:', endpoint);
  const params = { agent: { email: ENV.CURRENT_AGENT_EMAIL } };

  return (dispatch) => {
    dispatch({ type: "START_ORDER_EXPORT" });

    fetch(endpoint, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain",
        'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,
        'RETAILER-KEY': ENV.UNIQUE_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(params)
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: "START_ORDER_EXPORT_SUCCESS", data });
          // Inmediatamente después de iniciar, comenzar a hacer polling
          dispatch(fetchExportStatus());
        },
        (err) => {
          dispatch({ type: "START_ORDER_EXPORT_FAILURE", err });
        }
      ).catch((error) => {
        console.error('Start export error:', error);
        dispatch({ type: "START_ORDER_EXPORT_FAILURE", error });
      });
  };
};

export const startDetailedOrderExport = (searchParams) => {
  // 🔧 Los parámetros ya vienen en formato q[param], solo necesitamos enviarlos directamente
  const encodedParams = new URLSearchParams(searchParams).toString();
  const endpoint = `${ENV.SHOPS_BASE_URL}/api/v1/retailers/orders/detailed_export?${encodedParams}`;
  const params = { agent: { email: ENV.CURRENT_AGENT_EMAIL } };

  return (dispatch) => {
    dispatch({ type: "START_DETAILED_ORDER_EXPORT" });

    fetch(endpoint, {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain",
        'MERCATELY-AUTH-TOKEN': ENV.MERCATELY_AUTH_TOKEN,
        'RETAILER-KEY': ENV.UNIQUE_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(params)
    })
      .then((res) => res.json())
      .then(
        (data) => {
          dispatch({ type: "START_DETAILED_ORDER_EXPORT_SUCCESS", data });
          // Inmediatamente después de iniciar, comenzar a hacer polling
          dispatch(fetchExportStatus());
        },
        (err) => {
          dispatch({ type: "START_DETAILED_ORDER_EXPORT_FAILURE", err });
        }
      ).catch((error) => {
        console.error('Start detailed export error:', error);
        dispatch({ type: "START_DETAILED_ORDER_EXPORT_FAILURE", error });
      });
  };
};

// Nueva acción para manejar actualizaciones de webhook
export const updateExportStatusFromWebhook = (exportStatus) => {
  return (dispatch) => {
    console.log('🔔 [WEBHOOK ACTION] Actualizando estado desde webhook:', exportStatus);
    dispatch({ 
      type: "UPDATE_EXPORT_STATUS_FROM_WEBHOOK", 
      data: { export_status: exportStatus }
    });
  };
};
