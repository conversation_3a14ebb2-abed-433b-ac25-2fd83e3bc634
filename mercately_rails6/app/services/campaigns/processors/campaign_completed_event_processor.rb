module Campaigns
  module Processors
    # Procesador para eventos de finalización de campaña
    #
    # Este procesador se encarga de:
    # 1. Actualizar el estado de la campaña en la base de datos
    # 2. Limpiar los contadores en Redis
    # 3. Enviar notificaciones de finalización de campaña
    #
    # Se ejecuta cuando se recibe un evento campaign_completed_event desde Kafka
    class CampaignCompletedEventProcessor
      attr_reader :payload, :campaign_id, :total_messages, :sent_messages, :failed_messages

      # Inicializa el procesador con el payload del evento
      #
      # @param payload [Hash] El payload del evento
      def initialize(payload)
        @payload = payload
        @campaign_id = payload['campaign_id']
        @total_messages = payload['total_messages']
        @sent_messages = payload['sent_messages']
        @failed_messages = payload['failed_messages']
      end

      # Procesa el evento de finalización de campaña
      #
      # @return [Boolean] True si el procesamiento fue exitoso, false en caso contrario
      def process
        Rails.logger.info("Procesando evento campaign_completed_event para campaña #{campaign_id}")

        # Obtener la campaña
        campaign = Campaign.find(campaign_id)

        # Actualizar estado de la campaña
        update_campaign_status(campaign)

        # Limpiar contadores en Redis
        cleanup_redis_counters

        true
      rescue StandardError => e
        Rails.logger.error("Error al procesar evento campaign_completed_event: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        false
      end

      private

        # Actualiza el estado de la campaña en la base de datos
        #
        # @param campaign [Campaign] La campaña
        def update_campaign_status(campaign)
          # Determinar el estado final de la campaña
          if sent_messages.zero?
            # Si no se envió ningún mensaje, la campaña falló
            campaign.update(status: :failed, reason: :no_messages_sent)
            Rails.logger.info("Campaña #{campaign_id} marcada como fallida (no se enviaron mensajes)")
          else
            # Si se envió al menos un mensaje, la campaña se considera enviada
            campaign.update(status: :sent)
            Rails.logger.info("Campaña #{campaign_id} marcada como enviada (#{sent_messages} mensajes enviados)")
          end

          # NOTE: total_messages, sent_messages, failed_messages no existen en el modelo Campaign
          # Estas estadísticas se mantienen en Redis y en los eventos de Kafka
          Rails.logger.info("Estadísticas de campaña #{campaign_id}: " \
                            "total=#{total_messages}, sent=#{sent_messages}, failed=#{failed_messages}")
        end

        # Logs information about Redis counters cleanup
        #
        # NOTE: This method previously set additional Redis counters (sent, failed,
        # started_at, completed_at, status) but they were removed as they are not
        # used anywhere in the codebase. Only essential counters are maintained.
        def cleanup_redis_counters
          # Solo mantenemos los contadores esenciales: total y pending_messages
          # Los demás contadores (sent, failed, started_at, completed_at, status)
          # no se usan en el código y son remanentes de implementaciones anteriores

          Rails.logger.info("Contadores de Redis mantenidos para campaña #{campaign_id}")
          Rails.logger.info("- campaign:#{campaign_id}:total (para estadísticas)")
          Rails.logger.info("- campaign:#{campaign_id}:pending_messages (para control de flujo)")
        end
    end
  end
end
