require 'ostruct'

# Require the constants file explicitly
module Campaigns
  module Processors
    # Procesador responsable de procesar eventos de inicio de campaña recibidos desde Kafka.
    #
    # Este procesador:
    # - Valida el tipo de evento.
    # - Obtiene la campaña desde la base de datos.
    # - Itera sobre los clientes de la campaña, enviando mensajes.
    # - Produce un evento "message sent" independientemente del éxito de la entrega.
    #
    # El proceso está diseñado para **escalabilidad y testabilidad**, asegurando separación de responsabilidades.
    #
    # @example Procesando un evento de inicio de campaña
    #   processor = Campaigns::Processors::CampaignStartedEventProcessor.new
    #   processor.process({ "event_type" => "campaign_started_event", "campaign_id" => 123 })
    class CampaignStartedEventProcessor
      attr_reader :logger

      # Inicializa el procesador con las dependencias necesarias.
      #
      # @param logger [Logger] Instancia del logger.
      def initialize(logger: Rails.logger)
        @logger = logger
      end

      # Retorna la instancia del productor de mensajes.
      #
      # @return [Campaigns::MessageEventProducer] La instancia del productor de mensajes.
      def message_producer
        @message_producer ||= Campaigns::MessageEventProducer.new
      end

      # Procesa un evento recibido desde Kafka.
      #
      # - Asegura que el evento sea válido.
      # - Obtiene la campaña.
      # - Procesa clientes enviando mensajes.
      #
      # @param payload [Hash] El payload del evento.
      def process(payload)
        # Log the full payload for debugging
        logger.info("🔍 CAMPAIGN_STARTED_PROCESSOR: Received payload: #{payload.inspect}")

        unless valid_event_type?(payload, 'campaign_started_event')
          logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Invalid event type: #{payload['event_type']}")
          return
        end

        campaign_id = payload['campaign_id']
        logger.info("🚀 CAMPAIGN_STARTED_PROCESSOR: Processing campaign start event #{campaign_id}")

        # Log Redis connection status
        begin
          redis_info = Redis.current.info
          logger.info('✅ CAMPAIGN_STARTED_PROCESSOR: Redis connection successful. Version: ' \
                      "#{redis_info['redis_version']}")
        rescue StandardError => e
          logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Redis connection error: #{e.message}")
        end

        campaign = fetch_campaign(campaign_id)
        unless campaign
          logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Campaign not found: #{campaign_id}")
          return
        end

        logger.info("✅ CAMPAIGN_STARTED_PROCESSOR: Campaign found: #{campaign.name} (ID: #{campaign.id})")

        # Process campaign customers
        process_campaign_customers(campaign)

        logger.info("✅ CAMPAIGN_STARTED_PROCESSOR: Campaign processing completed for #{campaign_id}")
      rescue StandardError => e
        logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Error processing campaign #{campaign_id}: #{e.message}")
        logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Backtrace: #{e.backtrace.join("\n")}")
      end

      private

        # Validates the event type.
        #
        # @param payload [Hash] The event payload.
        # @param expected_type [String] The expected event type.
        # @return [Boolean] True if the event type is valid, false otherwise.
        def valid_event_type?(payload, expected_type)
          payload.is_a?(Hash) && payload['event_type'] == expected_type
        end

        # Fetches the campaign from the database.
        #
        # @param campaign_id [Integer] The campaign ID.
        # @return [Campaign, nil] The campaign instance or nil if not found.
        def fetch_campaign(campaign_id)
          Campaign.find_by(id: campaign_id)
        rescue StandardError => e
          logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Error fetching campaign #{campaign_id}: #{e.message}")
          nil
        end

        # Iterates over campaign customers and processes them individually.
        #
        # Uses `find_each` for efficient batch processing.
        # Handles the case when no customers are associated with the campaign.
        #
        # @param campaign [Campaign] The campaign instance.
        def process_campaign_customers(campaign)
          logger.info("🔄 CAMPAIGN_STARTED_PROCESSOR: Processing customers for campaign #{campaign.id}")

          # Verify if campaign has any customers
          begin
            customer_count = campaign.customers.count
            logger.info("🔢 CAMPAIGN_STARTED_PROCESSOR: Customer count query successful: #{customer_count}")
          rescue StandardError => e
            logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Error counting customers: #{e.message}")
            logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Backtrace: #{e.backtrace.join("\n")}")
            return
          end

          if customer_count.zero?
            logger.warn("⚠️ CAMPAIGN_STARTED_PROCESSOR: No customers found for campaign #{campaign.id}")
            return
          end

          logger.info("✅ CAMPAIGN_STARTED_PROCESSOR: Found #{customer_count} customers for campaign #{campaign.id}")

          # Inicializar los contadores en Redis directamente
          begin
            logger.info('🔄 CAMPAIGN_STARTED_PROCESSOR: Inicializando contadores en Redis...')

            # Obtener una conexión a Redis
            redis = Redis.current

            # Verificar que Redis está disponible
            redis_ping = redis.ping
            logger.info("✅ CAMPAIGN_STARTED_PROCESSOR: Redis disponible, respuesta a PING: #{redis_ping}")

            # Inicializar contadores - Simplificado para depender solo de pending_messages
            redis_key_total = "campaign:#{campaign.id}:total"
            redis_key_pending = "campaign:#{campaign.id}:pending_messages"

            # Usar una transacción para inicializar los contadores
            redis.multi do |transaction|
              transaction.set(redis_key_total, customer_count)
              transaction.set(redis_key_pending, customer_count)
            end

            logger.info("✅ CAMPAIGN_STARTED_PROCESSOR: Contadores inicializados en Redis para campaña #{campaign.id}")

            # Verificar que los contadores se inicializaron correctamente
            total = redis.get(redis_key_total).to_i
            pending = redis.get(redis_key_pending).to_i

            logger.info('📊 CAMPAIGN_STARTED_PROCESSOR: Contadores después de inicialización:')
            logger.info("   - #{redis_key_total} = #{total}")
            logger.info("   - #{redis_key_pending} = #{pending}")

            # Verificar que los valores son correctos
            if total != customer_count || pending != customer_count
              logger.error('❌ CAMPAIGN_STARTED_PROCESSOR: Los contadores no se inicializaron correctamente')
              logger.error("   - Esperado: #{customer_count}")
              logger.error("   - Total actual: #{total}")
              logger.error("   - Pendientes actual: #{pending}")
              return
            end

            logger.info('✅ CAMPAIGN_STARTED_PROCESSOR: Verificación de contadores exitosa')
          rescue StandardError => e
            logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Error al inicializar contadores en Redis: #{e.message}")
            logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Backtrace: #{e.backtrace.join("\n")}")
            return
          end

          # 🔄 PRUEBA: Habilitando la producción de eventos pending para el segundo paso
          logger.info('🔄 PRUEBA: Habilitando producción de eventos campaign_message_pending_event')
          logger.info("🔄 PRUEBA: Produciendo #{customer_count} eventos pending para la campaña #{campaign.id}")

          # Producir un evento campaign_message_pending_event para cada cliente
          logger.info('🔄 CAMPAIGN_STARTED_PROCESSOR: Starting to produce pending events for ' \
                      "#{customer_count} customers")

          # Obtener el productor del pool
          begin
            producer = Mercately::Kafka::ProducerPool.get_producer
            logger.info('✅ CAMPAIGN_STARTED_PROCESSOR: Productor obtenido del pool correctamente')

            # Verificar que el productor sea válido
            if producer.nil?
              logger.error('❌ CAMPAIGN_STARTED_PROCESSOR: El productor es nil')
              raise 'El productor es nil'
            end

            # Verificar la clase del productor
            logger.info("📊 CAMPAIGN_STARTED_PROCESSOR: Clase del productor: #{producer.class}")
          rescue StandardError => e
            logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Error al obtener el productor: #{e.message}")
            logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Backtrace: #{e.backtrace.join("\n")}")
            raise # Re-raise to be caught by the caller
          end

          # Procesar clientes en lotes
          processed_count = 0
          campaign.customers.find_each(batch_size: 100) do |customer|
            begin
              produce_pending_event_for_customer(campaign, customer, producer)
              processed_count += 1

              # Log progress every 50 customers
              if (processed_count % 50).zero?
                logger.info("🔄 CAMPAIGN_STARTED_PROCESSOR: Processed #{processed_count}/#{customer_count} customers")
              end
            rescue StandardError => e
              logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Error processing customer #{customer.id}: #{e.message}")
              # Continue processing other customers
            end
          end

          logger.info("✅ CAMPAIGN_STARTED_PROCESSOR: Completed processing #{processed_count} customers for campaign #{campaign.id}")
        ensure
          # Siempre devolver el productor al pool
          if producer
            Mercately::Kafka::ProducerPool.release_producer(producer)
            logger.info('✅ CAMPAIGN_STARTED_PROCESSOR: Productor devuelto al pool')
          end
        end

        # Produces a pending event for a specific customer.
        #
        # @param campaign [Campaign] The campaign instance.
        # @param customer [Customer] The customer instance.
        # @param producer [Object] The Kafka producer instance.
        def produce_pending_event_for_customer(campaign, customer, producer)
          event_payload = {
            event_type: 'campaign_message_pending_event',
            campaign_id: campaign.id,
            customer_id: customer.id,
            timestamp: Time.current.to_i
          }

          # Produce the event using the provided producer
          producer.produce(
            topic: 'mercately_campaign_events',
            payload: event_payload.to_json,
            key: "#{campaign.id}_#{customer.id}"
          )

          logger.debug("📤 CAMPAIGN_STARTED_PROCESSOR: Produced pending event for customer #{customer.id}")
        rescue StandardError => e
          logger.error("❌ CAMPAIGN_STARTED_PROCESSOR: Error producing pending event for customer #{customer.id}: #{e.message}")
          raise # Re-raise to be handled by the caller
        end
    end
  end
end
