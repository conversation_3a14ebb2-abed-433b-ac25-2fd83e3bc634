# Require the constants file explicitly
module Campaigns
  module Consumers
    # Consumer responsible for processing "message sent" events and tracking campaign progress.
    #
    # This consumer listens for `campaign_message_sent_event` messages, processes them, and updates
    # the campaign progress in Redis. It triggers campaign completion once all messages have been processed.
    #
    # Responsibilities:
    # - Validate the event type and required fields.
    # - Update Redis with message tracking information.
    # - Check if the campaign is completed and trigger the completion event.
    #
    # @example Processing a message sent event
    #   consumer = Campaigns::Consumers::MessageSentConsumer.new
    #   consumer.process({ "event_type" => "campaign_message_sent_event", "campaign_id" => 123 })
    class MessageSentConsumer
      attr_reader :logger

      # Initializes the consumer with dependencies.
      #
      # @param logger [Logger] Logger instance.
      def initialize(logger: Rails.logger)
        @logger = logger
      end

      # Returns the event producer instance.
      #
      # @return [Object] The event producer instance.
      def event_producer
        @event_producer ||= Mercately::Kafka::ProducerPool.get_producer
      end

      # Processes a message sent event received from Kafka.
      #
      # - Ensures the event type and required fields are valid.
      # - Tracks message progress in Redis.
      # - If all messages have been processed, triggers the campaign completion event.
      #
      # @param payload [Hash] The event payload.
      def process(payload)
        return unless valid_event?(payload)

        campaign_id = payload['campaign_id']
        message_id = payload['message_id']
        payload.fetch('status', 'sent').to_sym

        log_info("Processing message sent event for campaign #{campaign_id}, message #{message_id}")

        # Ya no verificamos si la campaña está marcada como completada en Redis
        # porque ya no estamos marcando la campaña como completada en Redis
        is_already_completed = false
        logger.warn("DIAGNOSTIC: Is campaign already marked as completed? #{is_already_completed} (always false now)")

        # Decrease pending messages count
        # Movido desde CampaignMessageConsumer para asegurar que solo se decremente
        # cuando el evento campaign_message_sent_event haya sido procesado correctamente
        redis_key = "campaign:#{campaign_id}:pending_messages"
        current_count = Redis.current.decr(redis_key).to_i
        logger.info("Decreased pending messages count for campaign #{campaign_id}: #{current_count} remaining")

        # Check if the counter is negative and correct it
        if current_count.negative?
          # Si el contador es negativo, corregirlo a cero
          logger.warn("⚠️ Pending messages count is negative (#{current_count}) for campaign #{campaign_id}, resetting to 0")
          Redis.current.set(redis_key, 0)
          current_count = 0
        end

        # Check the pending messages counter (after decrementing)
        pending_count = current_count
        logger.warn("DIAGNOSTIC: Pending messages counter: #{pending_count}")

        # Check if the campaign should be completed
        # La campaña está completa si el contador pending_messages es 0
        should_complete = pending_count.zero?
        logger.warn("DIAGNOSTIC: Should complete campaign? #{should_complete} (pending_count=#{pending_count}, is_already_completed=#{is_already_completed})")

        if should_complete
          # Check if the campaign is complete using the centralized service
          completion_service = Campaigns::CampaignCompletionService.new(
            campaign_id: campaign_id,
            event_producer: event_producer,
            logger: logger
          )

          logger.warn('DIAGNOSTIC: Calling completion_service.complete_if_needed')

          if completion_service.complete_if_needed
            log_success("Campaign #{campaign_id} completed successfully")
          else
            logger.warn('DIAGNOSTIC: completion_service.complete_if_needed returned false')
          end
        end
      rescue StandardError => e
        log_error('Error processing message sent event', e)
      ensure
        # Siempre devolver el productor al pool si lo obtuvimos
        if @event_producer
          Mercately::Kafka::ProducerPool.release_producer(@event_producer)
          logger.info('🔄 MESSAGE_SENT_CONSUMER: Productor devuelto al pool')
          @event_producer = nil
        end
      end

      private

        # Validates if the event type and required fields are present.
        #
        # @param payload [Hash] The event payload.
        # @return [Boolean] True if the event is valid.
        def valid_event?(payload)
          required_keys = %w[event_type campaign_id message_id]
          payload['event_type'] == 'campaign_message_sent_event' && required_keys.all? { |key| payload[key].present? }
        end

        # Logs the current campaign status.
        #
        # @param campaign_id [Integer] The campaign ID.
        def log_campaign_status(campaign_id)
          # Ignoramos TrackerService por ahora
          # stats = campaign_tracker.get_stats(campaign_id)
          # log_info("Campaign status: #{stats[:sent]} sent, #{stats[:failed]} failed, #{stats[:total]} total")

          # En su lugar, usamos el contador pending_messages de Redis
          pending_count = Redis.current.get("campaign:#{campaign_id}:pending_messages").to_i
          log_info("Campaign status: pending_messages=#{pending_count}")
        end

        # Logs an error message with exception details.
        #
        # @param message [String] The error message.
        # @param exception [StandardError] The exception instance.
        def log_error(message, exception)
          logger.error("#{message}: #{exception.message}")
        end

        # Logs an informational message.
        #
        # @param message [String] The message to log.
        def log_info(message)
          logger.info("ℹ️ #{message}")
        end

        # Logs a success message.
        #
        # @param message [String] The message to log.
        def log_success(message)
          logger.info("✅ #{message}")
        end
    end
  end
end
