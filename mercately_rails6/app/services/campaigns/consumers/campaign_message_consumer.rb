# frozen_string_literal: true

# Require the constants file explicitly
module Campaigns
  module Consumers
    # Processes campaign_message_pending_event events.
    #
    # This consumer is responsible for sending individual campaign messages
    # to customers and producing campaign_message_sent_event events.
    class CampaignMessageConsumer
      # No podemos incluir Mercately::Karafka::Base::Consumer porque es una clase, no un módulo
      # Implementamos los métodos necesarios directamente

      # Returns the logger instance.
      #
      # @return [Logger] The logger instance.
      def logger
        @logger ||= Rails.logger
      end

      # Processes a campaign_message_pending_event.
      #
      # @param payload [Hash] The event payload.
      def process(payload)
        logger.info('Processing campaign message pending event for campaign ' \
                    "#{payload['campaign_id']}, customer #{payload['customer_id']}")

        # Validate event type
        unless valid_event_type?(payload, 'campaign_message_pending_event')
          logger.error("Invalid event type: #{payload['event_type']}")
          return
        end

        # Fetch campaign and customer
        campaign = fetch_campaign(payload['campaign_id'])
        customer = fetch_customer(payload['customer_id'])

        # Validate campaign and customer
        if campaign.nil?
          logger.error("Campaign not found: #{payload['campaign_id']}")
          return
        end

        if customer.nil?
          logger.error("Customer not found: #{payload['customer_id']}")
          return
        end

        # Siempre enviamos mensajes reales en todos los entornos
        # En development/test, el método send_message_request en Whatsapp::Outbound::Msg
        # se encargará de simular la respuesta de Gupshup
        logger.info("Enviando mensaje real para campaña #{campaign.id} a cliente #{customer.id}")

        # Variables para el evento de mensaje enviado
        success = false
        message_id = "error_#{Time.now.to_i}_#{rand(1000)}"
        error_message = nil

        # Usar directamente el servicio de envío de mensajes de WhatsApp
        begin
          # Preparar los parámetros para el envío del mensaje
          # Generar un identificador único para cada mensaje de campaña
          # Usamos un timestamp con milisegundos y un UUID para garantizar unicidad
          unique_id = "#{(Time.current.to_f * 1000).to_i}_#{SecureRandom.uuid}"
          params = {
            gupshup_template_id: campaign.whatsapp_template.gupshup_template_id,
            template_params: campaign.customer_content_params(customer),
            template: 'true',
            type: get_content_type(campaign),
            campaign_id: campaign.id,
            message_identifier: "campaign_#{campaign.id}_customer_#{customer.id}_#{unique_id}"
          }

          # Añadir parámetros específicos según el tipo de mensaje
          if campaign.file.attached?
            params[:caption] = campaign.customer_details_template(customer).gsub(/(\r)/, '')
            params[:file_name] = campaign.file.filename.to_s
            params[:url] = campaign.file_url
          else
            params[:message] = campaign.customer_details_template(customer).gsub(/(\r)/, '')
          end

          # Usar nuestro servicio seguro específico para campañas
          logger.info("🔄 CAMPAIGNS: Usando WhatsappMessageSender seguro para campaña #{campaign.id},
                      cliente #{customer.id}")

          # Crear un identificador único para el mensaje
          message_identifier = "campaign_#{campaign.id}_customer_#{customer.id}_#{Time.now.to_i}_#{SecureRandom.uuid}"
          params[:message_identifier] = message_identifier

          # Verificar los parámetros del mensaje
          logger.info("🔍 Parámetros del mensaje: #{params.inspect}")

          # Usar el servicio de envío de WhatsApp
          logger.info('📡 CAMPAIGNS: Usando Campaigns::WhatsappMessageSender para envío')
          result = Campaigns::WhatsappMessageSender.call(campaign, customer, params)

          if result[:success]
            logger.info("✅ CAMPAIGNS: Mensaje enviado exitosamente con ID: #{result[:message].id}")
            message = result[:message]
            success = true
            message_id = message.gupshup_message_id || message.id.to_s
          else
            logger.error("❌ CAMPAIGNS: Error al enviar mensaje: #{result[:error]}")
            success = false
            message_id = "error_#{Time.now.to_i}_#{rand(1000)}"
            error_message = result[:error]
          end

          # Procesar el resultado exitoso
          if success && message
            customer.update_columns(campaignerized: true, campaign_id: campaign.id)

            # Notificar a los usuarios sobre el nuevo mensaje
            logger.info("Notificando a los usuarios sobre el nuevo mensaje ID: #{message.id}")
            begin
              # Actualizar last_chat_interaction explícitamente con la fecha actual
              logger.info("Actualizando last_chat_interaction para el cliente #{customer.id}")
              current_time = Time.current
              customer.update_columns(
                last_chat_interaction: current_time,
                ws_active: true
              )
              logger.info("last_chat_interaction actualizado para el cliente #{customer.id} con fecha #{current_time}")

              # Establecer el estado del chat como "resolved" para que aparezca en el filtro "Resueltos"
              logger.info("Estableciendo el estado del chat como resolved para el cliente #{customer.id}")
              begin
                # Actualizar directamente el estado del chat sin usar StatusChatUpdater
                logger.info("Actualizando directamente el estado del chat para el cliente #{customer.id}")
                customer.update(status_chat: 'resolved')
                logger.info("Estado del chat establecido como resolved para el cliente #{customer.id}")
              rescue StandardError => e
                logger.error("Error al establecer el estado del chat para el cliente #{customer.id}: #{e.message}")
                logger.error(e.backtrace.join("\n"))
              end

              # Enviar notificación a los usuarios
              Notifications::Web::Messages.new(message).broadcast!
              logger.info("Notificación enviada exitosamente para el mensaje ID: #{message.id}")
            rescue StandardError => e
              logger.error("Error al enviar notificación para el mensaje ID: #{message.id} - #{e.message}")
              logger.error(e.backtrace.join("\n"))
            end

            logger.info('✅ Mensaje enviado exitosamente para campaña ' \
                        "#{campaign.id}, cliente #{customer.id}, mensaje ID: #{message.id}")
          else
            logger.error('❌ Error al enviar mensaje para campaña ' \
                         "#{campaign.id}, cliente #{customer.id}: #{error_message}")
          end
        rescue StandardError => e
          logger.error('❌ Error inesperado al enviar mensaje para campaña ' \
                       "#{campaign.id}, cliente #{customer.id}: #{e.class.name} - #{e.message}")
          logger.error(e.backtrace.join("\n"))
          error_message = e.message
        end

        # Siempre producimos un evento de mensaje enviado para que la campaña pueda completarse
        # independientemente del resultado del envío
        event_payload = {
          event_type: 'campaign_message_sent_event',
          campaign_id: campaign.id,
          customer_id: customer.id,
          timestamp: Time.current.to_i,
          retailer_id: campaign.retailer_id,
          message_id: message_id,
          status: success ? 'sent' : 'failed'
        }

        # Añadir el mensaje de error si existe
        event_payload[:error_message] = error_message if error_message.present?

        logger.info('Produciendo evento de mensaje enviado para campaña ' \
                    "#{campaign.id}, cliente #{customer.id}, status: #{event_payload[:status]}")

        # Obtener un productor del pool
        producer = Mercately::Kafka::ProducerPool.get_producer
        logger.info('🔄 CAMPAIGN_MESSAGE_CONSUMER: Productor obtenido del pool')

        begin
          # Producir el evento de mensaje enviado
          result = producer.produce(
            topic: Campaigns::MessageEventProducer::TOPIC,
            payload: event_payload.to_json,
            key: campaign.id.to_s
          )

          # La gema mercately-kafka maneja la entrega asíncrona automáticamente
          # No necesitamos flush manual
          logger.info("✅ CAMPAIGN_MESSAGE_CONSUMER: Evento de mensaje enviado producido exitosamente: \
                      #{result.inspect}")
        rescue StandardError => e
          logger.error("❌ CAMPAIGN_MESSAGE_CONSUMER: Error al producir evento de mensaje enviado: #{e.message}")
          logger.error(e.backtrace.join("\n"))
          raise
        ensure
          # Siempre devolver el productor al pool
          Mercately::Kafka::ProducerPool.release_producer(producer)
          logger.info('🔄 CAMPAIGN_MESSAGE_CONSUMER: Productor devuelto al pool')
        end

        # El decremento del contador pending_messages se realiza en MessageSentConsumer
        # para asegurar que solo se decremente cuando el evento campaign_message_sent_event
        # haya sido procesado correctamente

        logger.info("✅ Processed campaign message pending event for campaign #{campaign.id}, customer #{customer.id}")
      rescue StandardError => e
        log_error('Error processing campaign message pending event', e)
      end

      private

        # Validates that the event type matches the expected type.
        #
        # @param payload [Hash] The event payload.
        # @param expected_type [String] The expected event type.
        # @return [Boolean] True if the event type matches.
        def valid_event_type?(payload, expected_type)
          payload['event_type'] == expected_type
        end

        # Fetches a campaign by ID.
        #
        # @param campaign_id [Integer] The campaign ID.
        # @return [Campaign, nil] The campaign instance or nil if not found.
        def fetch_campaign(campaign_id)
          Campaign.find_by(id: campaign_id)
        end

        # Fetches a customer by ID.
        #
        # @param customer_id [Integer] The customer ID.
        # @return [Customer, nil] The customer instance or nil if not found.
        def fetch_customer(customer_id)
          Customer.find_by(id: customer_id)
        end

        # Returns the message sender service class.
        #
        # @return [Class] The message sender service class.
        def message_sender_service_class
          # Usar MessageSenderService directamente en lugar de KafkaMessageSenderService
          # para evitar problemas con los parámetros
          Campaigns::MessageSenderService
        end

        # Logs an error with exception details.
        #
        # @param message [String] The error message.
        # @param exception [Exception] The exception instance.
        def log_error(message, exception)
          logger.error("#{message}: #{exception.message}")
          logger.error(exception.backtrace.join("\n"))
        end

        # Determines the content type of the message based on the campaign file.
        #
        # @param campaign [Campaign] The campaign instance.
        # @return [String] The content type (text, image, video, document).
        def get_content_type(campaign)
          return 'text' unless campaign.file.attached?

          case campaign.file.content_type
          when /image/ then 'image'
          when /video/ then 'video'
          else 'document'
          end
        end
    end
  end
end
