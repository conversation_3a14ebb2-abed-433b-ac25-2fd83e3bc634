module KarafkaLock
  # Ruta al archivo de bloqueo
  LOCK_FILE = Rails.root.join('tmp', 'karafka.lock')

  # Intenta adquirir el bloqueo para Karafka
  # @return [Boolean] true si se adquirió el bloqueo, false si ya está bloqueado
  def self.acquire
    return false if locked?

    # Crear el directorio tmp si no existe
    FileUtils.mkdir_p(File.dirname(LOCK_FILE))

    # Escribir el PID actual en el archivo de bloqueo
    File.open(LOCK_FILE, 'w') do |f|
      f.write("#{Process.pid}\n")
      f.write("#{Time.current}\n")
      f.write("#{NodeRole.hostname}\n")
    end

    Rails.logger.info("Karafka lock acquired by process #{Process.pid} on #{NodeRole.hostname}")
    true
  end

  # Libera el bloqueo de Karafka
  # @return [Boolean] true si se liberó el bloqueo, false si no existía
  def self.release
    return false unless File.exist?(LOCK_FILE)

    File.delete(LOCK_FILE)
    Rails.logger.info("Karafka lock released by process #{Process.pid} on #{NodeRole.hostname}")
    true
  end

  # Verifica si Karafka está bloqueado (ya en ejecución)
  # @return [Boolean] true si Karafka está bloqueado, false si no
  def self.locked?
    return false unless File.exist?(LOCK_FILE)

    # Leer el PID del archivo de bloqueo
    lines = File.readlines(LOCK_FILE)
    return false if lines.empty?

    pid = lines[0].to_i

    # Verificar si el proceso sigue en ejecución
    begin
      Process.kill(0, pid)
      # Si llegamos aquí, el proceso existe
      Rails.logger.info("Karafka is already running with PID #{pid}")
      true
    rescue Errno::ESRCH
      # El proceso no existe, el bloqueo es inválido
      Rails.logger.info('Found stale Karafka lock file. Removing it.')
      release
      false
    rescue StandardError => e
      # Otro error, asumir que el proceso existe por seguridad
      Rails.logger.warn("Error checking Karafka lock: #{e.message}")
      true
    end
  end

  # Obtiene información sobre el bloqueo actual
  # @return [Hash, nil] Información del bloqueo o nil si no está bloqueado
  def self.info
    return nil unless File.exist?(LOCK_FILE)

    lines = File.readlines(LOCK_FILE)
    return nil if lines.empty?

    {
      pid: lines[0].to_i,
      timestamp: lines[1]&.strip,
      hostname: lines[2]&.strip
    }
  end
end
