module Mia::Chatbot::ResponseHandler
  class Instagram < Base
    private

      def send_text_message(text)
        send_message(params: { message: text })
      end

      def send_reference(reference, caption = '')
        file_content_type = reference['content_type']
        file_url = reference['url']

        if file_content_type.start_with?('text/')
          send_text_message(file_url)
        else
          filename = get_filename_from_url(file_url)
          file_type = get_file_type(file_content_type)
          send_text_message(caption) if caption.present?

          params = { file_url:, file_type:, filename:, file_content_type: }
          send_message(params:)
        end
      end

      def send_message(params: {}, note: false, mia_flag: nil)
        instagram_message = InstagramMessage.create(
          customer: customer,
          id_client: customer.psid,
          facebook_retailer: retailer.facebook_retailer,
          text: params[:message].presence,
          sent_from_mercately: true,
          sent_by_retailer: true,
          url: params[:file_url].presence,
          file_url: params[:file_url].presence,
          file_type: params[:file_type].presence,
          file_content_type: params[:file_content_type].presence,
          filename: params[:filename].presence,
          payload: params.presence,
          sent_by_mia: true,
          mia_flag:,
          note:
        )

        { message: instagram_message }
      end

      def process_internal_message(mia_flag)
        send_message(mia_flag:)
      end

      def process_note_message(params)
        send_message(params:, note: true)
      end
  end
end
