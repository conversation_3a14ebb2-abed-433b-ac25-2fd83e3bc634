module Mia::Chatbot::ResponseHandler
  class Messenger < Instagram
    private

      def send_message(params: {}, note: false, mia_flag: nil)
        facebook_message = FacebookMessage.create(
          customer: customer,
          id_client: customer.psid,
          facebook_retailer: retailer.facebook_retailer,
          text: params[:message].presence,
          sent_from_mercately: true,
          sent_by_retailer: true,
          url: params[:file_url].presence,
          file_url: params[:file_url].presence,
          file_type: params[:file_type].presence,
          file_content_type: params[:file_content_type].presence,
          filename: params[:filename].presence,
          payload: params.presence,
          sent_by_mia: true,
          mia_flag:,
          note:
        )

        { message: facebook_message }
      end
  end
end
