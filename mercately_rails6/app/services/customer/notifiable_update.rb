class Customer::NotifiableUpdate < ApplicationService
  attr_reader :customer, :retailer

  def initialize(customer)
    @customer = customer
    @retailer = customer.retailer
  end

  def call
    notify_active_retailer_users
  end

  private

    def notify_active_retailer_users
      active_retailer_users.each do |user|
        publish_update_for(user)
      end
    end

    def active_retailer_users
      agent = customer.agent
      agents = agent.present? ? [agent] : retailer.retailer_users.all_customers.to_a
      agents | retailer.admins(agent&.id) | retailer.supervisors(agent&.id)
    end

    def publish_update_for(user)
      redis.publish(channel_name, build_message(user).to_json)
    rescue StandardError => e
      Rails.logger.error("Error al notificar al usuario #{user.id}: #{e.message}")
    end

    def build_message(user)
      {
        retailer_id: customer.retailer_id,
        customer_id: customer.id,
        room: user.id,
        status: true
      }
    end

    def redis
      @redis ||= Redis.new(url: ENV['REDIS_PROVIDER'] || 'redis://localhost:6379/1')
    end

    def channel_name
      'update_customer_chat_info'
    end
end
