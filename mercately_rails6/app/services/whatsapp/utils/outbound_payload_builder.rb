# frozen_string_literal: true

module Whatsapp
  module Utils
    class OutboundPayloadBuilder < ApplicationService
      attr_reader :retailer, :customer, :params, :from

      def initialize(retailer:, customer:, params:, from: 'gupshup')
        @retailer = retailer
        @customer = customer
        @params = params
        @from = from
      end

      def call
        build_payload
      end

      private

        def build_payload
          created = CreationTimeGetter.call(params: params, from: from)

          {
            retailer: retailer,
            customer: customer,
            whatsapp_message_id: extract_message_id,
            gupshup_message_id: extract_message_id,
            status: extract_status,
            direction: 'outbound',
            message_payload: extract_message_body,
            source: params.dig('payload', 'source'),
            destination: params.dig('payload', 'destination'),
            integration: from,
            channel: 'whatsapp',
            delivered_at: Time.zone.now.to_i,
            sent_at: params[:timestamp],
            created_at: created,
            updated_at: created
          }.compact
        end

        def extract_message_id
          params.dig('payload', 'id')
        end

        def extract_message_body
          message_type = params.dig('payload', 'type')

          case message_type
          when 'text'
            extract_text_body
          when 'image', 'video', 'audio', 'file', 'sticker'
            extract_media_body
          when 'location'
            extract_location_body
          when 'contact'
            extract_contact_body
          else
            extract_default_body
          end
        end

        def extract_text_body
          {
            type: 'text',
            text: params.dig('payload', 'payload', 'text')
          }.compact
        end

        def extract_media_body
          message_type = params.dig('payload', 'type')
          {
            type: message_type,
            caption: params.dig('payload', 'payload', 'caption'),
            url: params.dig('payload', 'payload', 'url'),
            filename: params.dig('payload', 'payload', 'filename'),
            mime_type: params.dig('payload', 'payload', 'contentType')
          }.compact
        end

        def extract_location_body
          {
            type: 'location',
            latitude: params.dig('payload', 'payload', 'latitude'),
            longitude: params.dig('payload', 'payload', 'longitude'),
            name: params.dig('payload', 'payload', 'name'),
            address: params.dig('payload', 'payload', 'address')
          }.compact
        end

        def extract_contact_body
          contacts = params.dig('payload', 'payload', 'contacts') || []

          {
            type: 'contact',
            payload: {
              payload: {
                contacts: contacts
              }
            }
          }.compact
        end

        def extract_default_body
          params.dig('payload', 'payload') || {}
        end

        def extract_status
          'sent'
        end
    end
  end
end
