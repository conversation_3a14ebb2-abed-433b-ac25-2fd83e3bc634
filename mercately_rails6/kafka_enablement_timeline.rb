# Script para ver cuándo se habilitó Kafka en cada retailer usando logs de auditoría
retailer_ids = [621, 1277, 27708, 2036, 5638, 17783, 21432, 9067, 10031, 24196, 8603, 4293, 4149, 7921, 9929]

puts "🔍 TIMELINE DE HABILITACIÓN DE KAFKA (USANDO AUDITORÍA)"
puts "=" * 80
puts "📅 Referencia: 23/07 habilitamos Kafka en ID 621"
puts

retailers_data = []

retailer_ids.each do |retailer_id|
  retailer = Retailer.find_by(id: retailer_id)

  if retailer.nil?
    puts "❌ Retailer #{retailer_id} no encontrado"
    next
  end

  # Buscar en logs de auditoría cuándo se cambió kafka_enabled
  kafka_audit = nil
  if defined?(Audited) && retailer.respond_to?(:audits)
    kafka_audit = retailer.audits
                         .where("audited_changes LIKE ?", "%kafka_enabled%")
                         .where("audited_changes LIKE ?", "%true%")
                         .order(:created_at)
                         .last
  end

  retailers_data << {
    id: retailer_id,
    name: retailer.name,
    kafka_enabled: retailer.kafka_enabled,
    updated_at: retailer.updated_at,
    kafka_enabled_at: kafka_audit&.created_at,
    audit_changes: kafka_audit&.audited_changes
  }
end

# Ordenar por fecha de habilitación de Kafka (si existe) o por updated_at
retailers_data.sort_by! { |r| r[:kafka_enabled_at] || r[:updated_at] }

puts "📊 RETAILERS ORDENADOS POR FECHA DE HABILITACIÓN DE KAFKA:"
puts "=" * 80

retailers_data.each do |data|
  status_icon = data[:kafka_enabled] ? "✅" : "❌"

  if data[:kafka_enabled_at]
    date_str = data[:kafka_enabled_at].strftime('%Y-%m-%d %H:%M:%S')
    days_ago = ((Time.current - data[:kafka_enabled_at]) / 1.day).round(1)
    source = "📋 AUDIT"
  else
    date_str = data[:updated_at].strftime('%Y-%m-%d %H:%M:%S')
    days_ago = ((Time.current - data[:updated_at]) / 1.day).round(1)
    source = "⚠️  UPDATED_AT"
  end

  puts "#{status_icon} #{data[:id].to_s.rjust(5)} | #{data[:name].ljust(25)} | #{date_str} (#{days_ago} días) | #{source}"

  if data[:audit_changes]
    puts "      Cambios: #{data[:audit_changes]}"
  end
end

puts "\n🔍 ANÁLISIS:"
puts "=" * 80

kafka_enabled = retailers_data.select { |r| r[:kafka_enabled] }
kafka_disabled = retailers_data.select { |r| !r[:kafka_enabled] }

puts "✅ Retailers con Kafka habilitado: #{kafka_enabled.count}"
kafka_enabled.each do |r|
  days_ago = ((Time.current - r[:updated_at]) / 1.day).round(1)
  puts "   - #{r[:name]} (#{r[:id]}): actualizado hace #{days_ago} días"
end

puts "\n❌ Retailers con Kafka deshabilitado: #{kafka_disabled.count}"
kafka_disabled.each do |r|
  days_ago = ((Time.current - r[:updated_at]) / 1.day).round(1)
  puts "   - #{r[:name]} (#{r[:id]}): actualizado hace #{days_ago} días"
end

# Buscar retailers actualizados después del 23/07
july_23 = Time.parse('2024-07-23')
recently_updated = retailers_data.select { |r| r[:updated_at] >= july_23 }

puts "\n📅 RETAILERS ACTUALIZADOS DESDE EL 23/07/2024:"
puts "=" * 80
if recently_updated.any?
  recently_updated.each do |r|
    status_icon = r[:kafka_enabled] ? "✅" : "❌"
    date_str = r[:updated_at].strftime('%Y-%m-%d %H:%M:%S')
    puts "#{status_icon} #{r[:name]} (#{r[:id]}): #{date_str}"
  end
else
  puts "⚠️  Ningún retailer fue actualizado desde el 23/07/2024"
  puts "   Esto podría indicar que las fechas de updated_at no reflejan"
  puts "   cuándo se habilitó kafka_enabled específicamente."
end

puts "\n💡 RECOMENDACIÓN:"
puts "Si las fechas de updated_at no son precisas para kafka_enabled,"
puts "podríamos revisar:"
puts "1. Logs de la aplicación del 23/07 en adelante"
puts "2. Commits en Git relacionados con kafka_enabled"
puts "3. Crear un campo kafka_enabled_at para futuras referencias"
