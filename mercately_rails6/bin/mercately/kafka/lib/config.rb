# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  class Config
    class << self
      def default_config
        # Validar que las variables requeridas estén configuradas
        required_vars = %w[KAFKA_BOOTSTRAP_SERVERS KAFKA_CLIENT_ID KAFKA_SECURITY_PROTOCOL]
        missing_vars = required_vars.select { |var| ENV[var].blank? }

        if missing_vars.any?
          raise "Variables de entorno faltantes para Kafka: #{missing_vars.join(', ')}"
        end

        {
          'bootstrap.servers': ENV['KAFKA_BOOTSTRAP_SERVERS'],
          'client.id': ENV['KAFKA_CLIENT_ID'],
          'security.protocol': ENV['KAFKA_SECURITY_PROTOCOL'],
          'sasl.mechanisms': ENV['KAFKA_SASL_MECHANISM'],
          'sasl.username': ENV['KAFKA_USERNAME'],
          'sasl.password': <PERSON>N<PERSON>['KAFKA_PASSWORD']
        }.compact
      end

      def ssl_config
        return {} if ENV['KAFKA_CA_CERT'].blank?

        { 'ssl.ca.location': ENV['KAFKA_CA_CERT'] }
      end

      def full_config
        default_config.merge(ssl_config)
      end

      # DEPRECADO: Los métodos set_default_* han sido eliminados.
      # Todas las variables de entorno deben configurarse externamente.
      # Consulta .env.example para ver las variables requeridas.
      def set_default_env_variables
        puts "⚠️  DEPRECADO: set_default_env_variables ya no establece valores por defecto"
        puts "💡 Configura todas las variables de entorno externamente"
        puts "💡 Ejecuta 'bin/validate-kafka-config' para validar la configuración"
      end
    end
  end
end
