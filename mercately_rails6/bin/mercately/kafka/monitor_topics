#!/usr/bin/env ruby
# frozen_string_literal: true

# Definir Logger::Severity si no existe (para compatibilidad con Ruby 3.2.5)
require 'logger'
unless defined?(::Logger::Severity)
  ::Logger.const_set(:Severity, Module.new)
  ::Logger::Severity.const_set(:DEBUG, 0)
  ::Logger::Severity.const_set(:INFO, 1)
  ::Logger::Severity.const_set(:WARN, 2)
  ::Logger::Severity.const_set(:ERROR, 3)
  ::Logger::Severity.const_set(:FATAL, 4)
  ::Logger::Severity.const_set(:UNKNOWN, 5)
end

require 'bundler/setup'
require 'active_support'
require 'active_support/core_ext'
require 'rdkafka'
require 'json'
require 'optparse'

# Implementación simple de colorización
class String
  def colorize(color)
    colors = {
      red: 31,
      green: 32,
      yellow: 33,
      blue: 34,
      magenta: 35,
      cyan: 36
    }
    "\e[#{colors[color]}m#{self}\e[0m"
  end

  def red; colorize(:red); end
  def green; colorize(:green); end
  def yellow; colorize(:yellow); end
  def blue; colorize(:blue); end
  def magenta; colorize(:magenta); end
  def cyan; colorize(:cyan); end
end

begin
  require 'dotenv'
  Dotenv.load if defined?(Dotenv)
rescue LoadError
  # La gema dotenv no está disponible, continuamos sin ella
  puts "Advertencia: dotenv no está disponible, las variables de entorno deben estar configuradas manualmente".yellow
end

KAFKA_ROOT = File.dirname(__FILE__)
require_relative 'lib/config'

# Configuración por defecto
timeout_seconds = 3600 # 1 hora por defecto
summary_mode = false
summary_interval = 10 # segundos
topics = []

# Procesar argumentos de línea de comandos
option_parser = OptionParser.new do |opts|
  opts.banner = "Uso: #{File.basename(__FILE__)} [opciones] [tópicos...]"

  opts.on('-s', '--summary', 'Modo resumen: solo muestra conteos periódicos de mensajes') do
    summary_mode = true
  end

  opts.on('-i', '--interval SECONDS', Integer, 'Intervalo para el modo resumen (por defecto: 10 segundos)') do |interval|
    summary_interval = interval
  end

  opts.on('-t', '--timeout SECONDS', Integer, 'Tiempo total de monitoreo (por defecto: 3600 segundos)') do |timeout|
    timeout_seconds = timeout
  end

  opts.on('-h', '--help', 'Muestra esta ayuda') do
    puts opts
    exit
  end
end

# Parsear argumentos
begin
  option_parser.parse!
  topics = ARGV.empty? ? ['campaign_control', 'campaign_messages', 'mercately_campaign_events'] : ARGV
rescue OptionParser::InvalidOption => e
  puts "Error: #{e.message}".red
  puts option_parser
  exit 1
end

# Mostrar configuración
puts "🔍 Monitoreando tópicos: #{topics.join(', ')}...".colorize(:cyan)
puts "⏱️  Duración: #{timeout_seconds} segundos".colorize(:yellow)
if summary_mode
  puts "📊 Modo resumen activado (intervalo: #{summary_interval} segundos)".colorize(:green)
end
puts "⏹️  Presiona Ctrl+C para detener el monitoreo".colorize(:yellow)

# Configurar el consumidor
config = {
  'bootstrap.servers': ENV['KAFKA_BOOTSTRAP_SERVERS'],
  'group.id': "mercately-monitor-#{Time.now.to_i}",
  'auto.offset.reset': 'latest',
  'enable.auto.commit': true
}

# Agregar configuración de seguridad si está presente
if ENV['KAFKA_SECURITY_PROTOCOL'] == 'SASL_SSL'
  config['security.protocol'] = 'SASL_SSL'
  config['sasl.mechanism'] = ENV['KAFKA_SASL_MECHANISM']
  config['sasl.username'] = ENV['KAFKA_USERNAME']
  config['sasl.password'] = ENV['KAFKA_PASSWORD']

  if ENV['KAFKA_CA_CERT'] && File.exist?(ENV['KAFKA_CA_CERT'])
    config['ssl.ca.location'] = ENV['KAFKA_CA_CERT']
  end
end

begin
  # Crear el consumidor
  consumer = Rdkafka::Config.new(config).consumer
  consumer.subscribe(*topics)

  puts "\n📡 Esperando mensajes... (Presiona Ctrl+C para salir)".colorize(:green)

  # Variables para seguimiento
  messages_by_topic = Hash.new(0)
  start_time = Time.now
  last_summary_time = Time.now

  # Función para mostrar el resumen
  show_summary = lambda do |messages_by_topic, elapsed_seconds, final = false|
    total_messages = messages_by_topic.values.sum
    rate = total_messages / [elapsed_seconds, 0.1].max # Evitar división por cero

    puts "\n" unless final
    puts "📊 #{final ? 'Resumen final' : 'Resumen parcial'} (#{elapsed_seconds.round(1)}s):".colorize(:green)
    messages_by_topic.each do |topic_name, count|
      puts "  - Tópico '#{topic_name}': #{count} mensaje(s)".colorize(:cyan)
    end
    puts "  📈 Total: #{total_messages} mensaje(s), #{rate.round(2)} msg/s".colorize(:yellow)
    puts "  ⏱️  Tiempo transcurrido: #{(Time.now - start_time).round(1)}s de #{timeout_seconds}s".colorize(:yellow)

    if messages_by_topic.empty?
      puts "  ⚠️  No se han recibido mensajes.".colorize(:yellow)
    end
  end

  # Consumir mensajes
  while (Time.now - start_time) < timeout_seconds
    message = consumer.poll(1000)

    if message
      topic = message.topic
      messages_by_topic[topic] += 1

      # En modo resumen, solo mostrar detalles si ha pasado el intervalo
      if summary_mode
        elapsed = Time.now - last_summary_time
        if elapsed >= summary_interval
          show_summary.call(messages_by_topic, elapsed)
          last_summary_time = Time.now
        end
      else
        # Modo detallado: mostrar cada mensaje
        begin
          payload = JSON.parse(message.payload)
          pretty_payload = JSON.pretty_generate(payload)

          puts "\n📨 Mensaje recibido en tópico '#{topic}':".colorize(:green)
          puts "  🔑 Key: #{message.key || 'nil'}".colorize(:cyan)
          puts "  📍 Partition: #{message.partition}".colorize(:cyan)
          puts "  📌 Offset: #{message.offset}".colorize(:cyan)
          # Convertir timestamp a formato legible
          timestamp_str = message.timestamp.is_a?(Numeric) ? Time.at(message.timestamp / 1000.0) : message.timestamp
          puts "  📅 Timestamp: #{timestamp_str}".colorize(:cyan)
          puts "  📦 Payload:".colorize(:cyan)
          puts pretty_payload.split("\n").map { |line| "    #{line}" }.join("\n").colorize(:yellow)

          # Mostrar información específica según el tipo de evento
          if payload['event_type']
            puts "  🏷️  Tipo de evento: #{payload['event_type']}".colorize(:magenta)

            case payload['event_type']
            when 'campaign_started', 'process_campaign'
              puts "  📊 Campaña ID: #{payload['campaign_id']}".colorize(:magenta)
              puts "  👥 Total de mensajes: #{payload['total_messages']}".colorize(:magenta)
            when 'campaign_message_sent'
              puts "  📊 Campaña ID: #{payload['campaign_id']}".colorize(:magenta)
              puts "  👤 Cliente ID: #{payload['customer_id']}".colorize(:magenta)
              puts "  📱 Mensaje ID: #{payload['message_id']}".colorize(:magenta)
            when 'campaign_completed'
              puts "  📊 Campaña ID: #{payload['campaign_id']}".colorize(:magenta)
              puts "  ✅ Mensajes exitosos: #{payload['success_count']}".colorize(:magenta)
              puts "  ❌ Mensajes fallidos: #{payload['failure_count']}".colorize(:magenta)
            when 'campaign_started_event'
              puts "  📊 Campaña ID: #{payload['campaign_id']}".colorize(:magenta)
              puts "  🏢 Retailer ID: #{payload['retailer_id']}".colorize(:magenta)
              puts "  🌐 Campaña Web ID: #{payload['campaign_web_id']}".colorize(:magenta)
              puts "  👥 Destinatarios estimados: #{payload['estimated_recipients']}".colorize(:magenta)
            when 'campaign_message_sent_event'
              puts "  📊 Campaña ID: #{payload['campaign_id']}".colorize(:magenta)
              puts "  👤 Cliente ID: #{payload['customer_id']}".colorize(:magenta)
              puts "  📱 Mensaje ID: #{payload['message_id']}".colorize(:magenta)
              puts "  📡 Canal: #{payload['channel']}".colorize(:magenta)
              puts "  🚦 Estado: #{payload['status']}".colorize(:magenta)
            when 'campaign_completed_event'
              puts "  📊 Campaña ID: #{payload['campaign_id']}".colorize(:magenta)
              if payload['duration_seconds']
                puts "  ⏱️ Duración (segundos): #{payload['duration_seconds']}".colorize(:magenta)
              end
              if payload['success_count']
                puts "  ✅ Mensajes exitosos: #{payload['success_count']}".colorize(:magenta)
              end
              if payload['failure_count']
                puts "  ❌ Mensajes fallidos: #{payload['failure_count']}".colorize(:magenta)
              end
            end
          end
        rescue JSON::ParserError
          puts "\n📨 Mensaje recibido en tópico '#{topic}' (no es JSON):".colorize(:green)
          puts "  🔑 Key: #{message.key || 'nil'}".colorize(:cyan)
          puts "  📍 Partition: #{message.partition}".colorize(:cyan)
          puts "  📌 Offset: #{message.offset}".colorize(:cyan)
          # Convertir timestamp a formato legible
          timestamp_str = message.timestamp.is_a?(Numeric) ? Time.at(message.timestamp / 1000.0) : message.timestamp
          puts "  📅 Timestamp: #{timestamp_str}".colorize(:cyan)
          puts "  📦 Payload: #{message.payload}".colorize(:yellow)
        end
      end
    end
  end

  puts "\n✅ Monitoreo finalizado.".colorize(:green)
  elapsed = Time.now - start_time
  show_summary.call(messages_by_topic, elapsed, true)
rescue Interrupt
  puts "\n⏹️  Monitoreo interrumpido por el usuario.".colorize(:yellow)
  elapsed = Time.now - start_time
  show_summary.call(messages_by_topic, elapsed, true)
rescue StandardError => e
  puts "\n❌ Error al monitorear tópicos: #{e.message}".colorize(:red)
  puts e.backtrace.join("\n").colorize(:red)
ensure
  consumer&.close
end
