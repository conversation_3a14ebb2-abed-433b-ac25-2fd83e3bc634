# frozen_string_literal: true

# Script rápido para verificar el estado de campañas Kafka
# Uso en consola: load 'lib/scripts/quick_kafka_check.rb'

puts "🔍 VERIFICACIÓN RÁPIDA - CAMPAÑAS KAFKA"
puts "=" * 50

# 1. Verificar retailers con Kafka habilitado
kafka_retailers = Retailer.where(kafka_enabled: true)
puts "🏪 Retailers con kafka_enabled=true: #{kafka_retailers.count}"

if kafka_retailers.any?
  kafka_retailers.limit(5).each do |retailer|
    puts "   - #{retailer.name} (ID: #{retailer.id})"
  end
  puts "   ... y #{kafka_retailers.count - 5} más" if kafka_retailers.count > 5
else
  puts "   ⚠️  No hay retailers con Kafka habilitado"
end

puts

# 2. Verificar campañas con sender_strategy = 'kafka'
kafka_campaigns = Campaign.where(sender_strategy: 'kafka')
puts "📊 Campañas con sender_strategy='kafka': #{kafka_campaigns.count}"

if kafka_campaigns.any?
  # Últimas 5 campañas Kafka
  puts "   📅 Últimas 5 campañas Kafka:"
  kafka_campaigns.order(created_at: :desc).limit(5).each do |campaign|
    retailer_name = campaign.retailer.name rescue "Retailer #{campaign.retailer_id}"
    puts "   - #{campaign.name} | #{retailer_name} | #{campaign.status} | #{campaign.created_at.strftime('%Y-%m-%d %H:%M')}"
  end
  
  # Resumen por estado
  puts
  puts "   📊 Resumen por estado:"
  status_counts = kafka_campaigns.group(:status).count
  status_counts.each do |status, count|
    icon = %w[cancelled pending in_process failed processing].include?(status) ? "❌" : "✅"
    puts "   #{icon} #{status}: #{count}"
  end
else
  puts "   ℹ️  No hay campañas con sender_strategy='kafka'"
end

puts

# 3. Verificar campañas recientes (últimos 7 días) de retailers con Kafka
recent_campaigns = Campaign.joins(:retailer)
                          .where(retailers: { kafka_enabled: true })
                          .where(created_at: 7.days.ago..Time.current)

puts "📅 Campañas de retailers con Kafka (últimos 7 días): #{recent_campaigns.count}"

if recent_campaigns.any?
  # Agrupar por sender_strategy
  strategy_counts = recent_campaigns.group(:sender_strategy).count
  puts "   📊 Por estrategia de envío:"
  strategy_counts.each do |strategy, count|
    puts "   - #{strategy}: #{count}"
  end
  
  # Campañas problemáticas
  problematic = recent_campaigns.where.not(status: 'sent')
  if problematic.any?
    puts
    puts "   🚨 Campañas problemáticas (estado != 'sent'):"
    problematic.limit(10).each do |campaign|
      retailer_name = campaign.retailer.name rescue "Retailer #{campaign.retailer_id}"
      puts "   ❌ #{campaign.name} | #{retailer_name} | #{campaign.status} | #{campaign.sender_strategy}"
    end
  else
    puts "   ✅ Todas las campañas recientes están en estado 'sent'"
  end
end

puts

# 4. Comandos útiles para investigación
puts "💡 COMANDOS ÚTILES:"
puts "   # Cargar el monitor completo:"
puts "   load 'lib/scripts/kafka_campaigns_monitor.rb'"
puts
puts "   # Ver detalles de un retailer específico:"
puts "   retailer = Retailer.find(RETAILER_ID)"
puts "   retailer.campaigns.where(sender_strategy: 'kafka').order(created_at: :desc)"
puts
puts "   # Verificar configuración de un retailer:"
puts "   retailer.kafka_enabled"
puts
puts "   # Ver campañas problemáticas:"
puts "   Campaign.where(sender_strategy: 'kafka').where.not(status: 'sent')"

puts
puts "✅ Verificación completada"
