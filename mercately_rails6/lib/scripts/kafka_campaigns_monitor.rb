# frozen_string_literal: true

# Script para monitorear campañas enviadas por Kafka
# Uso en consola: load 'lib/scripts/kafka_campaigns_monitor.rb'
# Luego ejecutar: KafkaCampaignsMonitor.run

class KafkaCampaignsMonitor
  # Estados que requieren investigación (no son 'sent')
  PROBLEMATIC_STATES = %w[cancelled pending in_process failed processing].freeze
  
  def self.run(options = {})
    new(options).call
  end

  def initialize(options = {})
    @days_back = options[:days_back] || 7
    @show_all = options[:show_all] || false
    @export_csv = options[:export_csv] || false
  end

  def call
    puts "🔍 MONITOREO DE CAMPAÑAS KAFKA"
    puts "=" * 60
    puts "📅 Período: últimos #{@days_back} días"
    puts "🕐 Generado: #{Time.current.strftime('%Y-%m-%d %H:%M:%S UTC')}"
    puts

    # Obtener retailers con Kafka habilitado
    kafka_retailers = get_kafka_retailers
    
    if kafka_retailers.empty?
      puts "⚠️  No se encontraron retailers con kafka_enabled = true"
      return
    end

    puts "🏪 Retailers con Kafka habilitado: #{kafka_retailers.count}"
    puts

    # Procesar cada retailer
    total_campaigns = 0
    problematic_campaigns = 0
    
    kafka_retailers.each do |retailer|
      campaigns = get_retailer_campaigns(retailer)
      next if campaigns.empty? && !@show_all

      total_campaigns += campaigns.count
      retailer_problematic = campaigns.select { |c| PROBLEMATIC_STATES.include?(c.status) }.count
      problematic_campaigns += retailer_problematic

      display_retailer_summary(retailer, campaigns, retailer_problematic)
    end

    display_global_summary(total_campaigns, problematic_campaigns, kafka_retailers.count)
    
    export_to_csv if @export_csv
  end

  private

  def get_kafka_retailers
    Retailer.where(kafka_enabled: true)
            .includes(:campaigns)
            .order(:name)
  end

  def get_retailer_campaigns(retailer)
    retailer.campaigns
            .where(created_at: @days_back.days.ago..Time.current)
            .where(sender_strategy: 'kafka')
            .order(created_at: :desc)
  end

  def display_retailer_summary(retailer, campaigns, problematic_count)
    puts "🏪 #{retailer.name} (ID: #{retailer.id})"
    puts "   📊 Total campañas Kafka: #{campaigns.count}"
    
    if campaigns.empty?
      puts "   ✅ Sin campañas en el período"
      puts
      return
    end

    # Mostrar resumen por estado
    status_summary = campaigns.group(:status).count
    status_summary.each do |status, count|
      icon = PROBLEMATIC_STATES.include?(status) ? "❌" : "✅"
      puts "   #{icon} #{status}: #{count}"
    end

    # Mostrar campañas problemáticas en detalle
    if problematic_count > 0
      puts "   🚨 CAMPAÑAS QUE REQUIEREN INVESTIGACIÓN:"
      
      campaigns.select { |c| PROBLEMATIC_STATES.include?(c.status) }.each do |campaign|
        puts "      ❌ #{campaign.name} (#{campaign.web_id})"
        puts "         📅 #{campaign.created_at.strftime('%Y-%m-%d %H:%M')}"
        puts "         📊 Estado: #{campaign.status}"
        puts "         🎯 Estrategia: #{campaign.sender_strategy}"
        puts "         👥 Alcance: #{campaign.customers_scope || 'N/A'} contactos"
        puts "         💰 Costo: $#{campaign.cost || 0}"
        puts "         📝 Razón: #{campaign.reason || 'N/A'}" if campaign.reason.present?
        puts
      end
    end

    puts "   " + "-" * 50
    puts
  end

  def display_global_summary(total_campaigns, problematic_campaigns, retailers_count)
    puts "📈 RESUMEN GLOBAL"
    puts "=" * 60
    puts "🏪 Retailers con Kafka: #{retailers_count}"
    puts "📊 Total campañas Kafka: #{total_campaigns}"
    puts "✅ Campañas exitosas: #{total_campaigns - problematic_campaigns}"
    puts "❌ Campañas problemáticas: #{problematic_campaigns}"
    
    if total_campaigns > 0
      success_rate = ((total_campaigns - problematic_campaigns).to_f / total_campaigns * 100).round(2)
      puts "📊 Tasa de éxito: #{success_rate}%"
    end
    
    puts
    
    if problematic_campaigns > 0
      puts "🚨 ACCIÓN REQUERIDA:"
      puts "   Se encontraron #{problematic_campaigns} campañas que requieren investigación."
      puts "   Revisa los detalles arriba para identificar posibles problemas en el pipeline de Kafka."
    else
      puts "✅ Todo parece estar funcionando correctamente!"
    end
    
    puts
    puts "💡 COMANDOS ÚTILES PARA INVESTIGACIÓN:"
    puts "   # Ver detalles de una campaña específica:"
    puts "   Campaign.find_by(web_id: 'CAMPAIGN_WEB_ID')"
    puts
    puts "   # Ver mensajes de una campaña:"
    puts "   Campaign.find_by(web_id: 'CAMPAIGN_WEB_ID').gupshup_whatsapp_messages"
    puts
    puts "   # Verificar configuración Kafka de un retailer:"
    puts "   Retailer.find(RETAILER_ID).kafka_enabled"
  end

  def export_to_csv
    # Implementación futura para exportar a CSV
    puts "📄 Exportación a CSV no implementada aún"
  end
end

# Métodos de conveniencia para usar en consola
def monitor_kafka_campaigns(days_back: 7, show_all: false)
  KafkaCampaignsMonitor.run(days_back: days_back, show_all: show_all)
end

def monitor_kafka_campaigns_all_retailers(days_back: 7)
  KafkaCampaignsMonitor.run(days_back: days_back, show_all: true)
end

# Instrucciones de uso
puts "📋 SCRIPT CARGADO: Kafka Campaigns Monitor"
puts "🚀 Uso:"
puts "   monitor_kafka_campaigns                    # Últimos 7 días"
puts "   monitor_kafka_campaigns(days_back: 30)     # Últimos 30 días"
puts "   monitor_kafka_campaigns_all_retailers      # Incluir retailers sin campañas"
puts "   KafkaCampaignsMonitor.run                  # Método directo"
